#!/usr/bin/env python3
"""
Unified Workflow - Simple solution that runs everything in sequence

This script:
1. Gets company name from user ONCE
2. Runs main workflow (saves aggregated_company.json)
3. Runs LinkedIn scraper (saves linkedin_company.json) 
4. Runs Gmail scraper (saves gmail_company.json)
5. Combines all data and regenerates final HTML report
"""

import os
import sys
import json
import subprocess
import time
from datetime import datetime

def get_company_name():
    """Get company name from user"""
    print("\n" + "="*60)
    print("🚀 UNIFIED PORTFOLIO ANALYZER")
    print("="*60)
    print("\nThis will collect data from ALL sources:")
    print("📝 Blog posts | 🔍 Google search | 🔴 Reddit | 📺 YouTube")
    print("🔗 LinkedIn activities | 📧 Gmail messages")
    print("="*60)
    
    while True:
        company_name = input("\nEnter company name: ").strip()
        if company_name:
            return company_name
        print("Please enter a valid company name.")

def run_main_workflow(company_name):
    """Run main.py and wait for completion"""
    print(f"\n{'='*50}")
    print("🔄 STEP 1: Running Main Workflow")
    print(f"{'='*50}")
    
    # Import and run main workflow directly
    try:
        from main import PortfolioAnalyzer
        analyzer = PortfolioAnalyzer()
        
        # Collect data for the company
        company_data = analyzer.collect_company_data(company_name)
        
        # Generate initial report
        companies_data = {company_name: company_data}
        analyzer.generate_reports(companies_data)
        
        print("✅ Main workflow completed successfully")
        return True
        
    except Exception as e:
        print(f"❌ Main workflow failed: {e}")
        return False

def run_linkedin_scraper(company_name):
    """Run LinkedIn scraper"""
    print(f"\n{'='*50}")
    print("🔄 STEP 2: Running LinkedIn Scraper")
    print(f"{'='*50}")
    
    try:
        from linkedin_scraper import get_company_linkedin_data
        linkedin_data = get_company_linkedin_data(company_name)
        
        # Save to JSON file
        filename = f"linkedin_{company_name.replace(' ', '_').replace('.', '').lower()}.json"
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(linkedin_data, f, indent=2, ensure_ascii=False)
        
        print(f"✅ LinkedIn data saved to {filename}")
        return True
        
    except Exception as e:
        print(f"⚠️ LinkedIn scraper error: {e}")
        # Create empty file so merge doesn't fail
        filename = f"linkedin_{company_name.replace(' ', '_').replace('.', '').lower()}.json"
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump([], f)
        print(f"📝 Created empty LinkedIn file: {filename}")
        return True

def run_gmail_scraper(company_name):
    """Run Gmail scraper"""
    print(f"\n{'='*50}")
    print("🔄 STEP 3: Running Gmail Scraper")
    print(f"{'='*50}")
    
    try:
        from gm import get_company_gmail_data
        gmail_data = get_company_gmail_data(company_name)
        
        # Save to JSON file
        filename = f"gmail_{company_name.replace(' ', '_').replace('.', '').lower()}.json"
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(gmail_data, f, indent=2, ensure_ascii=False)
        
        print(f"✅ Gmail data saved to {filename}")
        return True
        
    except Exception as e:
        print(f"⚠️ Gmail scraper error: {e}")
        # Create empty file so merge doesn't fail
        filename = f"gmail_{company_name.replace(' ', '_').replace('.', '').lower()}.json"
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump([], f)
        print(f"📝 Created empty Gmail file: {filename}")
        return True

def merge_all_data_and_generate_report(company_name):
    """Merge all data and generate final comprehensive report"""
    print(f"\n{'='*50}")
    print("🔄 STEP 4: Merging All Data & Generating Final Report")
    print(f"{'='*50}")
    
    try:
        # Load main data
        main_file = f"aggregated_{company_name.replace(' ', '_').replace('.', '').lower()}.json"
        with open(main_file, 'r', encoding='utf-8') as f:
            main_data = json.load(f)
        print(f"✅ Loaded main data: {main_data['total_articles']} articles")
        
        # Load LinkedIn data
        linkedin_file = f"linkedin_{company_name.replace(' ', '_').replace('.', '').lower()}.json"
        try:
            with open(linkedin_file, 'r', encoding='utf-8') as f:
                linkedin_data = json.load(f)
            print(f"✅ Loaded LinkedIn data: {len(linkedin_data)} items")
        except:
            linkedin_data = []
            print("⚠️ No LinkedIn data found")
        
        # Load Gmail data
        gmail_file = f"gmail_{company_name.replace(' ', '_').replace('.', '').lower()}.json"
        try:
            with open(gmail_file, 'r', encoding='utf-8') as f:
                gmail_data = json.load(f)
            print(f"✅ Loaded Gmail data: {len(gmail_data)} items")
        except:
            gmail_data = []
            print("⚠️ No Gmail data found")
        
        # Merge data
        all_articles = main_data.get('articles', [])
        
        # Add LinkedIn data with proper format
        for item in linkedin_data:
            all_articles.append({
                'headline': item.get('headline', ''),
                'description': item.get('description', ''),
                'url': item.get('url', ''),
                'image_url': item.get('image_url', ''),
                'full_content': item.get('full_content', ''),
                'sentiment': item.get('sentiment', 'neutral'),
                'sentiment_score': item.get('sentiment_score', 0.0),
                'summary': item.get('summary', ''),
                'source': 'linkedin',
                'date': item.get('date', 'Unknown')
            })
        
        # Add Gmail data with proper format
        for item in gmail_data:
            all_articles.append({
                'headline': item.get('headline', ''),
                'description': item.get('description', ''),
                'url': item.get('url', ''),
                'image_url': item.get('image_url', ''),
                'full_content': item.get('full_content', ''),
                'sentiment': item.get('sentiment', 'neutral'),
                'sentiment_score': item.get('sentiment_score', 0.0),
                'summary': item.get('summary', ''),
                'source': 'gmail',
                'date': item.get('date', 'Unknown')
            })
        
        # Update main data with merged articles
        main_data['articles'] = all_articles
        main_data['total_articles'] = len(all_articles)
        main_data['sources_count']['linkedin'] = len(linkedin_data)
        main_data['sources_count']['gmail'] = len(gmail_data)
        
        # Recalculate sentiment
        if all_articles:
            avg_sentiment = sum(article.get('sentiment_score', 0) for article in all_articles) / len(all_articles)
            main_data['overall_sentiment_score'] = avg_sentiment
            main_data['overall_sentiment'] = 'positive' if avg_sentiment >= 0.05 else 'negative' if avg_sentiment <= -0.05 else 'neutral'
        
        # Generate final report using existing GeminiProcessor
        from gemini_processor import GeminiProcessor
        processor = GeminiProcessor()
        
        # Update the prompt to include LinkedIn and Gmail in sources
        html_content = processor.generate_company_report(main_data)
        
        # Save final comprehensive report
        final_filename = f"final_comprehensive_report_{company_name.replace(' ', '_').replace('.', '').lower()}.html"
        with open(final_filename, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        print(f"✅ Final comprehensive report saved: {final_filename}")
        print(f"📊 Total articles: {len(all_articles)}")
        print(f"   - Main sources: {len(main_data['articles']) - len(linkedin_data) - len(gmail_data)}")
        print(f"   - LinkedIn: {len(linkedin_data)}")
        print(f"   - Gmail: {len(gmail_data)}")
        
        return final_filename
        
    except Exception as e:
        print(f"❌ Error merging data: {e}")
        import traceback
        traceback.print_exc()
        return None

def main():
    """Main execution"""
    try:
        # Get company name
        company_name = get_company_name()
        
        print(f"\n🎯 Starting comprehensive analysis for: {company_name}")
        print("⏱️ This will take several minutes...")
        
        # Step 1: Main workflow
        if not run_main_workflow(company_name):
            print("❌ Stopping due to main workflow failure")
            return
        
        # Step 2: LinkedIn scraper
        run_linkedin_scraper(company_name)
        
        # Step 3: Gmail scraper
        run_gmail_scraper(company_name)
        
        # Step 4: Merge and generate final report
        final_report = merge_all_data_and_generate_report(company_name)
        
        if final_report:
            print(f"\n{'='*60}")
            print("🎉 COMPREHENSIVE ANALYSIS COMPLETE!")
            print(f"{'='*60}")
            print(f"📊 Final Report: {final_report}")
            print(f"{'='*60}")
            
            # Open report
            try:
                import webbrowser
                open_browser = input(f"\nOpen final report in browser? (y/n): ").strip().lower()
                if open_browser in ['y', 'yes']:
                    webbrowser.open(f"file://{os.path.abspath(final_report)}")
            except ImportError:
                pass
        else:
            print("❌ Failed to generate final report")
            
    except KeyboardInterrupt:
        print("\n\n⚠️ Operation cancelled by user.")
    except Exception as e:
        print(f"\n❌ An error occurred: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
