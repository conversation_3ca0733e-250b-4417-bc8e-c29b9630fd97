#!/usr/bin/env python3
"""
Complete Pipeline Testing Script

This script performs comprehensive testing of the entire Market Intelligence Pipeline.
It tests each component individually and then the complete end-to-end flow.
"""

import os
import sys
import json
import tempfile
import time
from datetime import datetime
from typing import Dict, Any, List

def print_header(title: str):
    """Print formatted header"""
    print(f"\n{'='*60}")
    print(f"🧪 {title}")
    print(f"{'='*60}")

def print_test(test_name: str):
    """Print test name"""
    print(f"\n🔍 Testing: {test_name}")
    print("-" * 40)

def create_sample_html_report(company_name: str = "TestCompany") -> str:
    """Create a sample HTML report for testing"""
    html_content = f"""
    <!DOCTYPE html>
    <html>
    <head>
        <title>Market Intelligence Report - {company_name}</title>
        <meta charset="UTF-8">
    </head>
    <body>
        <h1>Market Intelligence Report for {company_name}</h1>
        
        <h2>Executive Summary</h2>
        <p>This is a comprehensive market intelligence report for {company_name}. 
        The company has shown strong performance in recent quarters with significant 
        growth in key market segments.</p>
        
        <h2>Market Analysis</h2>
        <p>The market analysis reveals several key trends affecting {company_name}:</p>
        <ul>
            <li>Increased demand for digital transformation services</li>
            <li>Growing competition in the technology sector</li>
            <li>Expansion opportunities in emerging markets</li>
            <li>Regulatory changes impacting business operations</li>
        </ul>
        
        <h2>Financial Performance</h2>
        <p>Recent financial data shows {company_name} has maintained steady revenue 
        growth with improved profit margins. Key financial metrics include:</p>
        <ul>
            <li>Revenue growth: 15% year-over-year</li>
            <li>Profit margin: 12.5%</li>
            <li>Market capitalization: $50B</li>
            <li>Employee count: 100,000+</li>
        </ul>
        
        <h2>Strategic Recommendations</h2>
        <p>Based on our analysis, we recommend the following strategic initiatives:</p>
        <ol>
            <li>Invest in artificial intelligence and machine learning capabilities</li>
            <li>Expand presence in high-growth markets</li>
            <li>Strengthen partnerships with key technology vendors</li>
            <li>Focus on sustainability and ESG initiatives</li>
        </ol>
        
        <h2>Risk Assessment</h2>
        <p>Key risks that {company_name} should monitor include:</p>
        <ul>
            <li>Economic uncertainty and market volatility</li>
            <li>Cybersecurity threats and data privacy concerns</li>
            <li>Supply chain disruptions</li>
            <li>Talent acquisition and retention challenges</li>
        </ul>
        
        <h2>Conclusion</h2>
        <p>{company_name} is well-positioned for continued growth, but must navigate 
        several challenges in the evolving market landscape. Strategic focus on 
        innovation and operational excellence will be key to success.</p>
        
        <footer>
            <p>Report generated on {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
            <p>Market Intelligence Pipeline - Automated Analysis</p>
        </footer>
    </body>
    </html>
    """
    
    # Create temporary file
    with tempfile.NamedTemporaryFile(mode='w', suffix='.html', delete=False, encoding='utf-8') as f:
        f.write(html_content)
        return f.name

class PipelineTestSuite:
    """Complete test suite for the Market Intelligence Pipeline"""
    
    def __init__(self):
        self.test_results = {}
        self.sample_html_file = None
        self.test_company = "TestCompany"
    
    def setup_test_environment(self) -> bool:
        """Set up test environment"""
        print_test("Test Environment Setup")
        
        try:
            # Create sample HTML report
            self.sample_html_file = create_sample_html_report(self.test_company)
            print(f"✅ Sample HTML report created: {self.sample_html_file}")
            
            # Check if all required modules can be imported
            required_modules = [
                'gcs_storage_manager',
                'vertex_ai_embedding_service', 
                'vertex_ai_matching_engine',
                'report_deduplication_manager',
                'market_intelligence_pipeline'
            ]
            
            for module_name in required_modules:
                try:
                    __import__(module_name)
                    print(f"✅ Module {module_name} imported successfully")
                except ImportError as e:
                    print(f"❌ Failed to import {module_name}: {e}")
                    return False
            
            return True
            
        except Exception as e:
            print(f"❌ Test environment setup failed: {e}")
            return False
    
    def test_gcs_storage_manager(self) -> bool:
        """Test GCS Storage Manager"""
        print_test("GCS Storage Manager")
        
        try:
            from gcs_storage_manager import GCSStorageManager
            
            # Initialize manager
            manager = GCSStorageManager()
            print("✅ GCS Storage Manager initialized")
            
            # Test report listing (should work even without uploads)
            reports = manager.list_company_reports(self.test_company)
            print(f"✅ Report listing works - found {len(reports)} existing reports")
            
            # Test upload (will fail without proper credentials, but we can test the logic)
            try:
                result = manager.upload_report(self.sample_html_file, self.test_company)
                if result['success']:
                    print(f"✅ Report upload successful: {result['gcs_url']}")
                else:
                    print(f"⚠️ Report upload failed (expected without credentials): {result.get('error')}")
            except Exception as e:
                print(f"⚠️ Report upload failed (expected without credentials): {e}")
            
            return True
            
        except Exception as e:
            print(f"❌ GCS Storage Manager test failed: {e}")
            return False
    
    def test_vertex_ai_embedding_service(self) -> bool:
        """Test Vertex AI Embedding Service"""
        print_test("Vertex AI Embedding Service")
        
        try:
            from vertex_ai_embedding_service import VertexAIEmbeddingService
            
            # Initialize service
            service = VertexAIEmbeddingService()
            print("✅ Vertex AI Embedding Service initialized")
            
            # Test HTML preprocessing
            with open(self.sample_html_file, 'r', encoding='utf-8') as f:
                html_content = f.read()
            
            clean_text = service.preprocess_html(html_content)
            print(f"✅ HTML preprocessing works - {len(html_content)} chars -> {len(clean_text)} chars")
            
            # Test text chunking
            chunks = service.chunk_text(clean_text)
            print(f"✅ Text chunking works - created {len(chunks)} chunks")
            
            # Test embedding generation (will fail without proper credentials)
            try:
                result = service.process_html_file(self.sample_html_file, self.test_company)
                if result['success']:
                    print(f"✅ Embedding generation successful: {result['num_chunks']} chunks")
                else:
                    print(f"⚠️ Embedding generation failed (expected without credentials): {result.get('error')}")
            except Exception as e:
                print(f"⚠️ Embedding generation failed (expected without credentials): {e}")
            
            return True
            
        except Exception as e:
            print(f"❌ Vertex AI Embedding Service test failed: {e}")
            return False
    
    def test_vertex_ai_matching_engine(self) -> bool:
        """Test Vertex AI Matching Engine Manager"""
        print_test("Vertex AI Matching Engine Manager")
        
        try:
            from vertex_ai_matching_engine import VertexAIMatchingEngineManager
            
            # Initialize manager
            manager = VertexAIMatchingEngineManager()
            print("✅ Vertex AI Matching Engine Manager initialized")
            
            # Test datapoint creation
            sample_embedding = [0.1] * 768  # Sample 768-dimensional vector
            datapoint = manager.create_datapoint(
                embedding=sample_embedding,
                company=self.test_company,
                date="2024-01-01",
                timestamp="2024-01-01T12:00:00",
                gcs_url=f"gs://test-bucket/{self.test_company}/2024-01-01/12-00-00.html",
                chunk_id=0,
                text_content="This is a test chunk."
            )
            print(f"✅ Datapoint creation works - ID: {datapoint['id']}")
            
            # Test query functionality (simulated)
            query_result = manager.query_similar_reports(
                query_embedding=sample_embedding,
                company_filter=self.test_company,
                num_neighbors=5
            )
            print(f"✅ Query functionality works - success: {query_result['success']}")
            
            return True
            
        except Exception as e:
            print(f"❌ Vertex AI Matching Engine Manager test failed: {e}")
            return False
    
    def test_deduplication_manager(self) -> bool:
        """Test Report Deduplication Manager"""
        print_test("Report Deduplication Manager")
        
        try:
            from report_deduplication_manager import ReportDeduplicationManager
            
            # Initialize manager
            manager = ReportDeduplicationManager()
            print("✅ Report Deduplication Manager initialized")
            
            # Test deduplication for test company
            result = manager.deduplicate_company_reports(self.test_company)
            print(f"✅ Deduplication works - found {result['reports_found']} reports")
            
            # Test statistics
            stats = manager.get_deduplication_statistics()
            print(f"✅ Statistics work - total deduplications: {stats['statistics'].get('total_deduplications', 0)}")
            
            return True
            
        except Exception as e:
            print(f"❌ Report Deduplication Manager test failed: {e}")
            return False
    
    def test_complete_pipeline(self) -> bool:
        """Test Complete Pipeline"""
        print_test("Complete Pipeline Integration")
        
        try:
            from market_intelligence_pipeline import MarketIntelligencePipeline
            
            # Initialize pipeline
            pipeline = MarketIntelligencePipeline(enable_deduplication=True)
            print("✅ Complete Pipeline initialized")
            
            # Health check
            health = pipeline.health_check()
            print(f"✅ Health check - overall status: {health['overall_status']}")
            
            for component, status in health['components'].items():
                status_icon = "✅" if status['status'] == 'healthy' else "⚠️" if status['status'] == 'disabled' else "❌"
                print(f"   {status_icon} {component}: {status['status']}")
            
            # Test pipeline processing (will partially fail without credentials)
            try:
                result = pipeline.process_report(self.sample_html_file, self.test_company)
                if result['success']:
                    print(f"✅ Pipeline processing successful")
                    print(f"   GCS URL: {result.get('final_gcs_url')}")
                    print(f"   Chunks: {result.get('chunks_processed', 0)}")
                    print(f"   Vectors: {result.get('vectors_stored', 0)}")
                else:
                    print(f"⚠️ Pipeline processing failed (expected without full credentials)")
                    print(f"   Error: {result.get('error')}")
                    
                    # Show which steps completed
                    for step_name, step_result in result.get('steps', {}).items():
                        status = "✅" if step_result.get('success', False) else "❌"
                        print(f"   {status} {step_name}")
            except Exception as e:
                print(f"⚠️ Pipeline processing failed (expected without credentials): {e}")
            
            # Test statistics
            stats = pipeline.get_pipeline_statistics()
            print(f"✅ Statistics work - total reports: {stats['statistics'].get('total_reports_processed', 0)}")
            
            return True
            
        except Exception as e:
            print(f"❌ Complete Pipeline test failed: {e}")
            return False
    
    def run_all_tests(self) -> Dict[str, bool]:
        """Run all tests"""
        print_header("Market Intelligence Pipeline - Complete Test Suite")
        
        tests = [
            ("Environment Setup", self.setup_test_environment),
            ("GCS Storage Manager", self.test_gcs_storage_manager),
            ("Vertex AI Embedding Service", self.test_vertex_ai_embedding_service),
            ("Vertex AI Matching Engine", self.test_vertex_ai_matching_engine),
            ("Deduplication Manager", self.test_deduplication_manager),
            ("Complete Pipeline", self.test_complete_pipeline)
        ]
        
        for test_name, test_function in tests:
            try:
                self.test_results[test_name] = test_function()
            except Exception as e:
                print(f"❌ Test {test_name} failed with exception: {e}")
                self.test_results[test_name] = False
        
        return self.test_results
    
    def print_summary(self):
        """Print test summary"""
        print_header("Test Summary")
        
        passed = 0
        total = len(self.test_results)
        
        for test_name, success in self.test_results.items():
            status = "✅ PASSED" if success else "❌ FAILED"
            print(f"{status} {test_name}")
            if success:
                passed += 1
        
        print(f"\nOverall: {passed}/{total} tests passed")
        
        if passed == total:
            print("\n🎉 All tests passed! Pipeline is ready for use.")
        elif passed >= total * 0.7:  # 70% pass rate
            print("\n⚠️ Most tests passed. Some failures expected without full GCP setup.")
            print("   Complete GCP setup for full functionality.")
        else:
            print("\n❌ Many tests failed. Please check your setup.")
            print("   Run setup_environment.py to diagnose issues.")
    
    def cleanup(self):
        """Clean up test environment"""
        if self.sample_html_file and os.path.exists(self.sample_html_file):
            try:
                os.unlink(self.sample_html_file)
                print(f"🧹 Cleaned up test file: {self.sample_html_file}")
            except Exception as e:
                print(f"⚠️ Failed to clean up test file: {e}")

def main():
    """Main test function"""
    test_suite = PipelineTestSuite()
    
    try:
        # Run all tests
        results = test_suite.run_all_tests()
        
        # Print summary
        test_suite.print_summary()
        
        # Determine exit code
        passed = sum(1 for success in results.values() if success)
        total = len(results)
        
        return passed >= total * 0.7  # 70% pass rate for success
        
    except KeyboardInterrupt:
        print("\n⚠️ Tests interrupted by user")
        return False
    except Exception as e:
        print(f"\n❌ Test suite failed: {e}")
        return False
    finally:
        test_suite.cleanup()

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
