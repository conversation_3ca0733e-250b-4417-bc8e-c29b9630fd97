#!/usr/bin/env python3
"""
Test script to demonstrate multi-company portfolio analysis
"""

from main import PortfolioAnalyzer

def test_multi_company():
    """Test the system with multiple companies"""
    analyzer = PortfolioAnalyzer()
    
    # Simulate collecting data for multiple companies
    print("Testing multi-company portfolio analysis...")
    
    # Test companies
    companies = ["Apple", "Microsoft"]
    
    print(f"Analyzing {len(companies)} companies: {', '.join(companies)}")
    
    # Collect data for all companies (simplified for demo)
    companies_data = {}
    for company in companies:
        print(f"\nProcessing {company}...")
        
        # Create mock data for demonstration
        mock_data = {
            'company_name': company,
            'overall_sentiment': 'positive',
            'overall_sentiment_score': 0.5,
            'total_articles': 10,
            'articles': [
                {
                    'headline': f'Great news about {company}',
                    'description': f'Positive developments at {company}',
                    'url': f'https://example.com/{company.lower()}',
                    'image_url': '',
                    'full_content': f'This is positive news about {company}',
                    'sentiment': 'positive',
                    'sentiment_score': 0.8,
                    'summary': f'Positive article about {company}',
                    'source': 'mock',
                    'date': '2025-01-26'
                }
            ],
            'sources_count': {
                'blog': 2,
                'google_search': 3,
                'reddit': 2,
                'youtube': 3
            }
        }
        companies_data[company] = mock_data
    
    # Generate reports
    output_file = analyzer.generate_reports(companies_data)
    
    print(f"\n✅ Multi-company report generated: {output_file}")
    return output_file

if __name__ == "__main__":
    test_multi_company()
