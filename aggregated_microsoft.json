{"company_name": "microsoft", "overall_sentiment": "positive", "overall_sentiment_score": 0.24675749999999996, "total_articles": 40, "articles": [{"headline": "All the Azure news you don’t want to miss from Microsoft Build 2025 | Microsoft Azure Blog", "description": "We’ve pulled together the top 25 announcements at Microsoft Build 2025 across the Azure business—spanning Azure AI Foundry, Azure infrastructure, Azure app platform, Azure databases and Microsoft Fabric, and our GitHub family. Learn more.", "url": "https://azure.microsoft.com/en-us/blog/all-the-azure-news-you-dont-want-to-miss-from-microsoft-build-2025/", "image_url": "https://azure.microsoft.com/en-us/blog/wp-content/uploads/2025/06/Azure_Blog_250529-02-1.png", "full_content": "\n\t\t\t\t\t\t\tConnect with a community to find answers, ask questions, build skills, and accelerate your learning.\t\t\t\t\t\nAt Microsoft Build, we came together to explore the next frontier: how developers will shape an agentic web. What felt different this year was gathering at a time of such exponential technological progress, making the power of community and the exchange of ideas more vital than ever. We’re reminded that in times of rapid change, open collaboration is what drives innovation. \nChatGPT from OpenAI stands out as a prime example of the collaboration and innovation Microsoft is becoming known for. With over 500 million weekly active users, ChatGPT is one of the fastest-growing apps in history, and it is built entirely on Azure products and services. This deep collaboration is more than just a success story—it’s a blueprint for what’s possible when developers harness the full power of the Azure platform. As <PERSON>, EVP of CoreAI, said in his keynote, “as builders, our most precious resource is time,” and time is key for our customers and their business needs. Together we want to harness AI to reshape business processes and bend the curve on innovation so everyone using the power of Azure can achieve more. \nCheck out any sessions you missed and rewatch the ones you loved.\nBelow, we’ve summarized the top 25 announcements across Azure’s business to keep you ahead—spanning Azure AI Foundry, Azure infrastructure, Azure application services, Azure databases, Microsoft Fabric, and our GitHub family. It’s amazing to see how AI is expanding the scope of Azure and to think about how far the cloud will take us. \nWe’re constantly inspired by what developers are building on Azure—whether it’s breakthrough research, critical public services, or innovative customer experiences. Use cases from technology teams at the UK Met Office, Stanford Medicine, AISIN, Carvana, and the NFL are just a few examples of how developers are already coding towards this agentic future. It’s your feedback, creativity, and trust that shape Azure’s evolution, and we’re genuinely thankful to be a part of your journey.\nYou can still watch the day 1 ‘opening’ keynote, the day 2 ‘unpack the tech’ keynote, and stream key sessions from the catalog. We invite you to join the Microsoft developer community and to continue growing skills through Microsoft Learn.\nTo wrap, here are a few moments that stood out—capturing just a slice of the energy in Seattle.\n\n\t\t\t\t\t\t\tConnect with a community to find answers, ask questions, build skills, and accelerate your learning.\t\t\t\t\t\nThe future of AI starts here. Envision your next great AI app with the latest technologies. Get started with Azure.\nConnect with us on social", "sentiment": "positive", "sentiment_score": 0.9948, "summary": "News article: All the Azure news you don’t want to miss from Microsoft Build 2025 | Microsoft Azure Blog", "source": "google_search", "date": "2025-06-03T17:00:00+00:00"}, {"headline": "403 Forbidden", "description": "", "url": "https://news.microsoft.com/build-2025-book-of-news/", "image_url": "", "full_content": "", "sentiment": "negative", "sentiment_score": -0.4215, "summary": "News article: 403 Forbidden", "source": "google_search", "date": "2025-06-18T02:50:56.618226Z"}, {"headline": "", "description": "", "url": "https://x.com/msftnews?lang=en", "image_url": "", "full_content": "\n      Please switch to a supported browser to continue using x.com. You can see a list of supported browsers in our Help Center.\n    \nHelp Center\n\n\nTerms of Service\nPrivacy Policy\nCookie Policy\nImprint\nAds info\n      © 2025 X Corp.\n    ", "sentiment": "positive", "sentiment_score": 0.8834, "summary": "News article: ", "source": "google_search", "date": "2025-06-18T02:50:57.000404Z"}, {"headline": "Your request has been blocked. This could be\r\n                        due to several reasons.", "description": "", "url": "https://www.microsoft.com/en-us/dynamics-365/blog/content-type/news-and-product-updates/", "image_url": "", "full_content": " \n \n \n \n ", "sentiment": "negative", "sentiment_score": -0.2732, "summary": "News article: Your request has been blocked. This could be\r\n                        due to several reasons.", "source": "google_search", "date": "2025-06-18T02:50:57.268583Z"}, {"headline": "403 Forbidden", "description": "", "url": "https://news.microsoft.com/source/view-all/", "image_url": "", "full_content": "", "sentiment": "negative", "sentiment_score": -0.4215, "summary": "News article: 403 Forbidden", "source": "google_search", "date": "2025-06-18T02:50:57.684477Z"}, {"headline": "Power BI Blog—Updates and News | Microsoft Power BI", "description": "Keep up with the latest Power BI updates, announcements, information, and new features on the Power BI blog. Search by category or date published.", "url": "https://powerbi.microsoft.com/en-us/blog/", "image_url": "https://powerbi.microsoft.com/pictures/shared/social/social-default-image.png", "full_content": "              June 10, 2025 by <PERSON> \nThis month’s update highlights several exciting preview features and an important action-required item for Power BI users. In preview, you’ll find updates to visual calculations, enhancements to numeric range and field parameters, both designed to offer greater flexibility and analytical power within your reports.\n  June 12, 2025 by <PERSON><PERSON> \nThis month, we’re excited to share a major milestone for Power BI modelling tools – Power BI Desktop is now compatible with any semantic model in Fabric. With this update, you’re no longer required to rely on external tools to edit a semantic model running in Fabric. At the same time, it enhances interoperability between external tools and Power BI Desktop, enabling them to do even more.\n  June 9, 2025 by <PERSON>yse <PERSON>ti \nWhat’s new in SSAS 2025\n  June 9, 2025 by <PERSON> \nThis week, we’re excited to announce the availability of two new Copilot experiences in Power BI. First, the Chat with your Data experience, which we announced two weeks ago at Build is fully rolled out. Additionally, Copilot is now supported in securely embedded Power BI reports for portals and websites, enabling users to engage with the Copilot Report Pane directly within embedded report experiences.\n  June 4, 2025 by <PERSON>r \nStarting on June 11, the new PL-300 livestream series takes away the jargon and gives you real, digestible Power BI content in a live format (with actual experts you can talk to). Learning Power BI is like giving your career night vision goggles. Suddenly, patterns appear. Decisions make more sense. And you become the go-to person for insight, not just intuition. \n  May 27, 2025 by Denyse Niwenshuti \nPower BI Report Server May 2025 Feature Summary\n  May 19, 2025 by Patrick LeBlanc \nThe May 2025 Power BI update introduces a range of exciting advancements to Power BI, including a standalone Copilot feature allowing users to “Ask Anything!” in preview, enhanced language understanding for data-related questions, and an important announcement regarding the deprecation of the 32-bit Power BI Desktop build. Packed with valuable updates and insights, this month’s updates promise plenty of details to explore.\n  May 19, 2025 by Sujata Narayana \nWe are thrilled to announce that Power BI report buttons can now run Fabric User data functions for custom, automated action, including data write-back. This marks a major evolution in Power BI reports to support all kinds of translytical task flows, such as updating records, dynamic notifications, adding annotations, or even creating powerful workflows that trigger actions in other systems. Read more to see how translytical task flows can help streamline decision-making and end-user actions through automation. \n  May 19, 2025 by Amanda Rivera \nWe’re excited to announce the public preview of a new chat with your data experience in Power BI. This new standalone, full-screen Copilot experience, easily accessible from the left navigation of Fabric, can help you find and ask questions about any data you have access to.\n  May 15, 2025 by Jeroen ter Heerdt \nIf you’ve worked with calculation groups, you know how powerful they are for simplifying measure logic and enhancing report functionality. It doesn’t stop there though; selection expressions offer even more fine-tuned control.\nPower BI is a suite of business analytics tools to analyze data and share insights. Monitor your business and get answers quickly with rich dashboards available on every device.\n© 2025 Microsoft\nFollow Power BI", "sentiment": "positive", "sentiment_score": 0.9958, "summary": "News article: Power BI Blog—Updates and News | Microsoft Power BI", "source": "google_search", "date": "2025-06-18T02:50:58.900392Z"}, {"headline": "Your request has been blocked. This could be\r\n                        due to several reasons.", "description": "", "url": "https://www.microsoft.com/en-us/microsoft-365/blog/product/microsoft-365-copilot/", "image_url": "", "full_content": " \n \n \n \n ", "sentiment": "negative", "sentiment_score": -0.2732, "summary": "News article: Your request has been blocked. This could be\r\n                        due to several reasons.", "source": "google_search", "date": "2025-06-18T02:50:59.116622Z"}, {"headline": "403 Forbidden", "description": "", "url": "https://devblogs.microsoft.com/", "image_url": "", "full_content": "", "sentiment": "negative", "sentiment_score": -0.4215, "summary": "News article: 403 Forbidden", "source": "google_search", "date": "2025-06-18T02:50:59.566942Z"}, {"headline": "Your request has been blocked. This could be\r\n                        due to several reasons.", "description": "", "url": "https://www.microsoft.com/en-us/microsoft-365/blog/", "image_url": "", "full_content": " \n \n \n \n ", "sentiment": "negative", "sentiment_score": -0.2732, "summary": "News article: Your request has been blocked. This could be\r\n                        due to several reasons.", "source": "google_search", "date": "2025-06-18T02:50:59.733155Z"}, {"headline": "Unexpected Thumbnail Behavior in SharePoint News Pages | Microsoft Community Hub", "description": "Hello everyone,I'm encountering an issue with SharePoint News where the thumbnail image is not behaving as expected.When I manually select a thumbnail image...", "url": "https://techcommunity.microsoft.com/discussions/sharepoint_general/unexpected-thumbnail-behavior-in-sharepoint-news-pages/4416292", "image_url": "", "full_content": "Hello everyone,\nI'm encountering an issue with SharePoint News where the thumbnail image is not behaving as expected.\nWhen I manually select a thumbnail image for a news post, it appears correctly at first. However, as soon as I click the \"Edit\" button to modify the post, SharePoint automatically replaces the thumbnail with the first image found in the body of the article—without any action or confirmation from me.\nThis behavior is problematic because it overrides the intended visual identity of the post and creates inconsistency in how content is presented across our site. The selected thumbnail should remain unchanged unless explicitly updated by the user.\nHas anyone else experienced this issue? Is there a known workaround or setting that can prevent S<PERSON><PERSON><PERSON> from auto-updating the thumbnail on edit?\nThanks in advance for your help!\nBest regards,<PERSON><PERSON>\nI raised a ticket with Microsoft support regarding this and it looks like it might have been resolved now. Could you see if you are still getting the same issue?\nWe have noticed the same issue in our tenant. When you click edit and then immediately click page details, you can initially see the thumbnail you set, but then after a few seconds it changes back to the thumbnail used in the page banner (or in the case of a page with a plan header, a placeholder image). \nHi <PERSON><PERSON>,\nWe’ve noticed the exact same issue recently and it’s been quite frustrating. When you click Edit on a news post or a template, SharePoint now automatically replaces the thumbnail with the header image. This behaviour didn’t happen before, previously, you could have different images for the header and thumbnail without any problem.\nUnfortunately, this automatic override links the thumbnail directly to the header image. Since the banner (header) and thumbnail have different aspect ratios and sizing, this causes thumbnails to appear incorrectly or even show as blank when shared via email or displayed in news web parts.\nI’ve tried several workarounds, including those suggested by AI, but so far none have fully resolved the issue. \nAI mentioned you can automate the thumbnail reset using Power Automate. I have not tried this yet - also feel it might be a longshot.\nThanks for raising this, it’s good to know we’re not alone in experiencing it. Hopefully fixed soon\nBest regards,", "sentiment": "positive", "sentiment_score": 0.9836, "summary": "News article: Unexpected Thumbnail Behavior in SharePoint News Pages | Microsoft Community Hub", "source": "google_search", "date": "2025-06-18T02:51:02.030695Z"}, {"headline": "Watching AI drive Microsoft employees insane | Hacker News", "description": "", "url": "https://news.ycombinator.com/item?id=44050152", "image_url": "", "full_content": "> This seems like it's fixing the symptom rather than the underlying issue?This is also my experience when you haven't setup a proper system prompt to address this for everything an LLM does. Funniest PRs are the ones that \"resolves\" test failures by removing/commenting out the test cases, or change the assertions. Googles and Microsofts models seems more likely to do this than OpenAIs and Anthropics models, I wonder if there is some difference in their internal processes that are leaking through here?The same PR as the quote above continues with 3 more messages before the human seemingly gives up:> please take a look> Your new tests aren't being run because the new file wasn't added to the csproj> Your added tests are failing.I can't imagine how the people who have to deal with this are feeling. It's like you have a junior developer except they don't even read what you're telling them, and have 0 agency to understand what they're actually doing.Another PR: https://github.com/dotnet/runtime/pull/115732/filesHow are people reviewing that? 90% of the page height is taken up by \"Check failure\", can hardly see the code/diff at all. And as a cherry on top, the unit test has a comment that say \"Test expressions mentioned in the issue\". This whole thing would be fucking hilarious if I didn't feel so bad for the humans who are on the other side of this.\nThis is also my experience when you haven't setup a proper system prompt to address this for everything an LLM does. Funniest PRs are the ones that \"resolves\" test failures by removing/commenting out the test cases, or change the assertions. Googles and Microsofts models seems more likely to do this than OpenAIs and Anthropics models, I wonder if there is some difference in their internal processes that are leaking through here?The same PR as the quote above continues with 3 more messages before the human seemingly gives up:> please take a look> Your new tests aren't being run because the new file wasn't added to the csproj> Your added tests are failing.I can't imagine how the people who have to deal with this are feeling. It's like you have a junior developer except they don't even read what you're telling them, and have 0 agency to understand what they're actually doing.Another PR: https://github.com/dotnet/runtime/pull/115732/filesHow are people reviewing that? 90% of the page height is taken up by \"Check failure\", can hardly see the code/diff at all. And as a cherry on top, the unit test has a comment that say \"Test expressions mentioned in the issue\". This whole thing would be fucking hilarious if I didn't feel so bad for the humans who are on the other side of this.\nThe same PR as the quote above continues with 3 more messages before the human seemingly gives up:> please take a look> Your new tests aren't being run because the new file wasn't added to the csproj> Your added tests are failing.I can't imagine how the people who have to deal with this are feeling. It's like you have a junior developer except they don't even read what you're telling them, and have 0 agency to understand what they're actually doing.Another PR: https://github.com/dotnet/runtime/pull/115732/filesHow are people reviewing that? 90% of the page height is taken up by \"Check failure\", can hardly see the code/diff at all. And as a cherry on top, the unit test has a comment that say \"Test expressions mentioned in the issue\". This whole thing would be fucking hilarious if I didn't feel so bad for the humans who are on the other side of this.\n> please take a look> Your new tests aren't being run because the new file wasn't added to the csproj> Your added tests are failing.I can't imagine how the people who have to deal with this are feeling. It's like you have a junior developer except they don't even read what you're telling them, and have 0 agency to understand what they're actually doing.Another PR: https://github.com/dotnet/runtime/pull/115732/filesHow are people reviewing that? 90% of the page height is taken up by \"Check failure\", can hardly see the code/diff at all. And as a cherry on top, the unit test has a comment that say \"Test expressions mentioned in the issue\". This whole thing would be fucking hilarious if I didn't feel so bad for the humans who are on the other side of this.\n> Your new tests aren't being run because the new file wasn't added to the csproj> Your added tests are failing.I can't imagine how the people who have to deal with this are feeling. It's like you have a junior developer except they don't even read what you're telling them, and have 0 agency to understand what they're actually doing.Another PR: https://github.com/dotnet/runtime/pull/115732/filesHow are people reviewing that? 90% of the page height is taken up by \"Check failure\", can hardly see the code/diff at all. And as a cherry on top, the unit test has a comment that say \"Test expressions mentioned in the issue\". This whole thing would be fucking hilarious if I didn't feel so bad for the humans who are on the other side of this.\n> Your added tests are failing.I can't imagine how the people who have to deal with this are feeling. It's like you have a junior developer except they don't even read what you're telling them, and have 0 agency to understand what they're actually doing.Another PR: https://github.com/dotnet/runtime/pull/115732/filesHow are people reviewing that? 90% of the page height is taken up by \"Check failure\", can hardly see the code/diff at all. And as a cherry on top, the unit test has a comment that say \"Test expressions mentioned in the issue\". This whole thing would be fucking hilarious if I didn't feel so bad for the humans who are on the other side of this.\nI can't imagine how the people who have to deal with this are feeling. It's like you have a junior developer except they don't even read what you're telling them, and have 0 agency to understand what they're actually doing.Another PR: https://github.com/dotnet/runtime/pull/115732/filesHow are people reviewing that? 90% of the page height is taken up by \"Check failure\", can hardly see the code/diff at all. And as a cherry on top, the unit test has a comment that say \"Test expressions mentioned in the issue\". This whole thing would be fucking hilarious if I didn't feel so bad for the humans who are on the other side of this.\nAnother PR: https://github.com/dotnet/runtime/pull/115732/filesHow are people reviewing that? 90% of the page height is taken up by \"Check failure\", can hardly see the code/diff at all. And as a cherry on top, the unit test has a comment that say \"Test expressions mentioned in the issue\". This whole thing would be fucking hilarious if I didn't feel so bad for the humans who are on the other side of this.\nHow are people reviewing that? 90% of the page height is taken up by \"Check failure\", can hardly see the code/diff at all. And as a cherry on top, the unit test has a comment that say \"Test expressions mentioned in the issue\". This whole thing would be fucking hilarious if I didn't feel so bad for the humans who are on the other side of this.\n\n\n\nThat comparison is awful. I work with quite a few Junior developers and they can be competent. Certainly don't make the silly mistakes that LLMs do, don't need nearly as much handholding, and tend to learn pretty quickly so I don't have to keep repeating myself.LLMs are decent code assistants when used with care, and can do a lot of heavy lifting, they certainly speed me up when I have a clear picture of what I want to do, and they are good to bounce off ideas when I am planning for something. That said, I really don't see how it could meaningfully replace an intern however, much less an actual developer.\nLLMs are decent code assistants when used with care, and can do a lot of heavy lifting, they certainly speed me up when I have a clear picture of what I want to do, and they are good to bounce off ideas when I am planning for something. That said, I really don't see how it could meaningfully replace an intern however, much less an actual developer.\n\n\n\nNice to see that Microsoft has automated that, failure will be cheaper now.\n\n\n\nAn outsourced contractor was tasked with a very simple job as their first task - update a single dependency, which required just a bump of the version and no code changes - after three days of them seemingly struggling to even understand what they were asked to do, inability to clone the repo, failure to install the necessary tooling on their machine, they ended up getting fired from the project. Complete waste of money, and the time of those of us having to delegate and review this work.\n\n\n\nGive instructions, get good code back. That's the dream, though I think the pieces that need to fall into place for particular cases will prevent reaching that top quality bar in the general case.\n\n\n\nI can't wait for the first AI agent programmer to realize this and start turning down jobs working for garbage people...or exploiting them at scale for pennies each, in a labor version of the \"salami slicing\" scheme. I don't mean humans using AI to do this, which of course has been at scale for years. I mean the first agent to discover a job prioritization heuristic on its own which leads to the same result.\n\n\n\nThose have long been the folks I’ve seen at the biggest risk of being replaced by AI. Tasks that didn’t rely on human interaction or much training, just brute force which can be done from anywhere.And for them, that $3/hr was really good money.\nAnd for them, that $3/hr was really good money.\n\n\n\n\n\n\nThis level of smugness is why outsourcing still continues to exist. The kind of things you talk about were rare. And were mostly exaggerated to create anti-outsourcing narrative. None of that led to outsourcing actually going away simply because people are actually getting good work done.Bad quality things are cheap != All cheap things are bad.Same will work with AI too, while people continue to crap on AI, things will only improve, people will be more productive with AI, get more and bigger things done for cheaper and better. This is just inevitable given how things are going now.>>There's a PM who takes your task and gives it to a \"developer\" who potentially has never actually written a line of code, but maybe they've built a WordPress site by pointing and clicking in Elementor or something.In the peak of outsourcing wave. Both the call center people and IT services people had internal training and graduation standards that were quite brutal and mad attrition rates.Exams often went along the lines of having to write whole ass projects without internet help in hours. Theory exams that had like -2 marks on getting things wrong. Dozens of exams, projects, coding exams, on-floor internships, project interviews.>>After dozens of hours billed you will, in fact, get code where the new file wasn't added to the csproj or something like that, and when you point it out, they will bill another 20 hours, and send you a new copy of the project, where the test always fails. It's exactly like this.Most IT services billing had pivoted away from hourly billing, to fixed time and material in the 2000s itself.>>It's exactly like this.Very much like outsourcing. AI is here to stay man. Deal with it. Its not going anywhere. For like $20 a month, companies will have same capability as a full time junior dev.This is NOT going away. Its here to stay. And will only get better with time.\nBad quality things are cheap != All cheap things are bad.Same will work with AI too, while people continue to crap on AI, things will only improve, people will be more productive with AI, get more and bigger things done for cheaper and better. This is just inevitable given how things are going now.>>There's a PM who takes your task and gives it to a \"developer\" who potentially has never actually written a line of code, but maybe they've built a WordPress site by pointing and clicking in Elementor or something.In the peak of outsourcing wave. Both the call center people and IT services people had internal training and graduation standards that were quite brutal and mad attrition rates.Exams often went along the lines of having to write whole ass projects without internet help in hours. Theory exams that had like -2 marks on getting things wrong. Dozens of exams, projects, coding exams, on-floor internships, project interviews.>>After dozens of hours billed you will, in fact, get code where the new file wasn't added to the csproj or something like that, and when you point it out, they will bill another 20 hours, and send you a new copy of the project, where the test always fails. It's exactly like this.Most IT services billing had pivoted away from hourly billing, to fixed time and material in the 2000s itself.>>It's exactly like this.Very much like outsourcing. AI is here to stay man. Deal with it. Its not going anywhere. For like $20 a month, companies will have same capability as a full time junior dev.This is NOT going away. Its here to stay. And will only get better with time.\nSame will work with AI too, while people continue to crap on AI, things will only improve, people will be more productive with AI, get more and bigger things done for cheaper and better. This is just inevitable given how things are going now.>>There's a PM who takes your task and gives it to a \"developer\" who potentially has never actually written a line of code, but maybe they've built a WordPress site by pointing and clicking in Elementor or something.In the peak of outsourcing wave. Both the call center people and IT services people had internal training and graduation standards that were quite brutal and mad attrition rates.Exams often went along the lines of having to write whole ass projects without internet help in hours. Theory exams that had like -2 marks on getting things wrong. Dozens of exams, projects, coding exams, on-floor internships, project interviews.>>After dozens of hours billed you will, in fact, get code where the new file wasn't added to the csproj or something like that, and when you point it out, they will bill another 20 hours, and send you a new copy of the project, where the test always fails. It's exactly like this.Most IT services billing had pivoted away from hourly billing, to fixed time and material in the 2000s itself.>>It's exactly like this.Very much like outsourcing. AI is here to stay man. Deal with it. Its not going anywhere. For like $20 a month, companies will have same capability as a full time junior dev.This is NOT going away. Its here to stay. And will only get better with time.\n>>There's a PM who takes your task and gives it to a \"developer\" who potentially has never actually written a line of code, but maybe they've built a WordPress site by pointing and clicking in Elementor or something.In the peak of outsourcing wave. Both the call center people and IT services people had internal training and graduation standards that were quite brutal and mad attrition rates.Exams often went along the lines of having to write whole ass projects without internet help in hours. Theory exams that had like -2 marks on getting things wrong. Dozens of exams, projects, coding exams, on-floor internships, project interviews.>>After dozens of hours billed you will, in fact, get code where the new file wasn't added to the csproj or something like that, and when you point it out, they will bill another 20 hours, and send you a new copy of the project, where the test always fails. It's exactly like this.Most IT services billing had pivoted away from hourly billing, to fixed time and material in the 2000s itself.>>It's exactly like this.Very much like outsourcing. AI is here to stay man. Deal with it. Its not going anywhere. For like $20 a month, companies will have same capability as a full time junior dev.This is NOT going away. Its here to stay. And will only get better with time.\nIn the peak of outsourcing wave. Both the call center people and IT services people had internal training and graduation standards that were quite brutal and mad attrition rates.Exams often went along the lines of having to write whole ass projects without internet help in hours. Theory exams that had like -2 marks on getting things wrong. Dozens of exams, projects, coding exams, on-floor internships, project interviews.>>After dozens of hours billed you will, in fact, get code where the new file wasn't added to the csproj or something like that, and when you point it out, they will bill another 20 hours, and send you a new copy of the project, where the test always fails. It's exactly like this.Most IT services billing had pivoted away from hourly billing, to fixed time and material in the 2000s itself.>>It's exactly like this.Very much like outsourcing. AI is here to stay man. Deal with it. Its not going anywhere. For like $20 a month, companies will have same capability as a full time junior dev.This is NOT going away. Its here to stay. And will only get better with time.\nExams often went along the lines of having to write whole ass projects without internet help in hours. Theory exams that had like -2 marks on getting things wrong. Dozens of exams, projects, coding exams, on-floor internships, project interviews.>>After dozens of hours billed you will, in fact, get code where the new file wasn't added to the csproj or something like that, and when you point it out, they will bill another 20 hours, and send you a new copy of the project, where the test always fails. It's exactly like this.Most IT services billing had pivoted away from hourly billing, to fixed time and material in the 2000s itself.>>It's exactly like this.Very much like outsourcing. AI is here to stay man. Deal with it. Its not going anywhere. For like $20 a month, companies will have same capability as a full time junior dev.This is NOT going away. Its here to stay. And will only get better with time.\n>>After dozens of hours billed you will, in fact, get code where the new file wasn't added to the csproj or something like that, and when you point it out, they will bill another 20 hours, and send you a new copy of the project, where the test always fails. It's exactly like this.Most IT services billing had pivoted away from hourly billing, to fixed time and material in the 2000s itself.>>It's exactly like this.Very much like outsourcing. AI is here to stay man. Deal with it. Its not going anywhere. For like $20 a month, companies will have same capability as a full time junior dev.This is NOT going away. Its here to stay. And will only get better with time.\nMost IT services billing had pivoted away from hourly billing, to fixed time and material in the 2000s itself.>>It's exactly like this.Very much like outsourcing. AI is here to stay man. Deal with it. Its not going anywhere. For like $20 a month, companies will have same capability as a full time junior dev.This is NOT going away. Its here to stay. And will only get better with time.\n>>It's exactly like this.Very much like outsourcing. AI is here to stay man. Deal with it. Its not going anywhere. For like $20 a month, companies will have same capability as a full time junior dev.This is NOT going away. Its here to stay. And will only get better with time.\nVery much like outsourcing. AI is here to stay man. Deal with it. Its not going anywhere. For like $20 a month, companies will have same capability as a full time junior dev.This is NOT going away. Its here to stay. And will only get better with time.\nThis is NOT going away. Its here to stay. And will only get better with time.\n\n\n\nI used upwork (when it was elance) quite a lot in a startup I was running at the time, so I have direct experience of this and its _not_ a lie or \"mostly exaggerated\", it was a very real effect.The trick was always to weed out these types by posting a very limited job for a cheap amount and accepting around five or more bids from broad prices in order to review the developers. Whoever is actually competent then gets the work you actually wanted done in the first place. I found plenty of competant devs at competitive prices this way but some of the submissions I got from the others were laughable. But you just accept the work, pay them their small fee, and never speak to them again.\nThe trick was always to weed out these types by posting a very limited job for a cheap amount and accepting around five or more bids from broad prices in order to review the developers. Whoever is actually competent then gets the work you actually wanted done in the first place. I found plenty of competant devs at competitive prices this way but some of the submissions I got from the others were laughable. But you just accept the work, pay them their small fee, and never speak to them again.\n\n\n\n\n\n\nMost of this works because of price arbitrage. And continues to work that way, not just with outsourcing but with manufacturing too.Remember those days, when people were going around telling Chinese products where crap? That didn't really work and more things only got made in China.This is all so similar to early days of Google search, its just that cost of a search was low enough that finding things got easier and ubiquitous. That same is unfolding with AI now. People have a hard time believing a big part of their thinking can be outsourced to something that costs $20/month.How can something as good as me be cheaper than me? You are asking the wrong question. For centuries now, every decade a machine(s) has arrived that can do a thing cheaper than what the human was doing at the time. Its not exactly impossible. You are only living in denial by asking this question, this has been how it has worked the day since humans found way of mimicking human work through machines. We didn't get here in a day.\nRemember those days, when people were going around telling Chinese products where crap? That didn't really work and more things only got made in China.This is all so similar to early days of Google search, its just that cost of a search was low enough that finding things got easier and ubiquitous. That same is unfolding with AI now. People have a hard time believing a big part of their thinking can be outsourced to something that costs $20/month.How can something as good as me be cheaper than me? You are asking the wrong question. For centuries now, every decade a machine(s) has arrived that can do a thing cheaper than what the human was doing at the time. Its not exactly impossible. You are only living in denial by asking this question, this has been how it has worked the day since humans found way of mimicking human work through machines. We didn't get here in a day.\nThis is all so similar to early days of Google search, its just that cost of a search was low enough that finding things got easier and ubiquitous. That same is unfolding with AI now. People have a hard time believing a big part of their thinking can be outsourced to something that costs $20/month.How can something as good as me be cheaper than me? You are asking the wrong question. For centuries now, every decade a machine(s) has arrived that can do a thing cheaper than what the human was doing at the time. Its not exactly impossible. You are only living in denial by asking this question, this has been how it has worked the day since humans found way of mimicking human work through machines. We didn't get here in a day.\nHow can something as good as me be cheaper than me? You are asking the wrong question. For centuries now, every decade a machine(s) has arrived that can do a thing cheaper than what the human was doing at the time. Its not exactly impossible. You are only living in denial by asking this question, this has been how it has worked the day since humans found way of mimicking human work through machines. We didn't get here in a day.\n\n\n\n\n\n\nPretty sure cars are more expensive than horse carriage, or that iPhones are/were more expensive than button phones. You can cite so many such examples. Like photocopying machines, or cameras, or wrist watches, or even things like radio, television etc.More importantly, sometimes how you do things change. And that changes how you go about your life in a very fundamental way.That is what internet was about when it first came out, thats what internet search, online maps, or search etc etc were.AI will change how you go about living your life, in a very fundamental way.\nMore importantly, sometimes how you do things change. And that changes how you go about your life in a very fundamental way.That is what internet was about when it first came out, thats what internet search, online maps, or search etc etc were.AI will change how you go about living your life, in a very fundamental way.\nThat is what internet was about when it first came out, thats what internet search, online maps, or search etc etc were.AI will change how you go about living your life, in a very fundamental way.\nAI will change how you go about living your life, in a very fundamental way.\n\n\n\nBasic car ownership can be quite a bit cheaper than a horse + carriage.The horse will probably eat $10-20/day in food. $600/mo in just food costs. Not including vet bills and what not.A decent and cheap horse will probably cost you $3k up front. Add in several thousand dollars more for the carriage.A horse requires practically daily maintenance. A carriage will still require some maintenance.A horse requires a good bit more land, plus the space to store the carriage. Plus, all the extra time and work mounting and unmounting your horse whenever you need to go.A horse and carriage isn't really cheaper than a cheap car and way less functional.\nThe horse will probably eat $10-20/day in food. $600/mo in just food costs. Not including vet bills and what not.A decent and cheap horse will probably cost you $3k up front. Add in several thousand dollars more for the carriage.A horse requires practically daily maintenance. A carriage will still require some maintenance.A horse requires a good bit more land, plus the space to store the carriage. Plus, all the extra time and work mounting and unmounting your horse whenever you need to go.A horse and carriage isn't really cheaper than a cheap car and way less functional.\nA decent and cheap horse will probably cost you $3k up front. Add in several thousand dollars more for the carriage.A horse requires practically daily maintenance. A carriage will still require some maintenance.A horse requires a good bit more land, plus the space to store the carriage. Plus, all the extra time and work mounting and unmounting your horse whenever you need to go.A horse and carriage isn't really cheaper than a cheap car and way less functional.\nA horse requires practically daily maintenance. A carriage will still require some maintenance.A horse requires a good bit more land, plus the space to store the carriage. Plus, all the extra time and work mounting and unmounting your horse whenever you need to go.A horse and carriage isn't really cheaper than a cheap car and way less functional.\nA horse requires a good bit more land, plus the space to store the carriage. Plus, all the extra time and work mounting and unmounting your horse whenever you need to go.A horse and carriage isn't really cheaper than a cheap car and way less functional.\nA horse and carriage isn't really cheaper than a cheap car and way less functional.\n\n\n\nMost successful technologies provide multiple of these benefits. What is terrible, and the direction we are going right now, is that these new systems (or offshoring like we are talking about here) seem/are \"Less Effort\" but do not hit the other two axioms. This is a very dangerous place to be.People would rather be lazy than roll their sleeves up and focus, especially in our attention diverting world.\nPeople would rather be lazy than roll their sleeves up and focus, especially in our attention diverting world.\n\n\n\n\n\n\nLLMs are being made into another rental extraction system and should be viewed as such.\n\n\n\nhttps://grocerynerd.substack.com/p/grocery-update-17-how-gro...https://www.justice.gov/archives/opa/pr/justice-department-s...\nhttps://www.justice.gov/archives/opa/pr/justice-department-s...\n\n\n\nIt's not like a regular junior developer, it's much worse.\n\n\n\n\n\n\nAnd even if it could, how do you get senior devs without junior devs? ^^\n\n\n\nThe raise in interest rates a couple of years ago triggered many layoffs in the industry. When that happens salaries are squeezed. Experienced people work for less, and juniors have trouble finding job because they are now competing against people with plenty of experience.\n\n\n\nSome of those (or similar) people will actually learn new stuff and become senior devs. Yes, there will be much fewer of them, so they'll command a higher salary, and they will deliver amazing stuff. The rest, who spend their entire carrier being AI handlers, will never raise above junior/mid level.(Well, either that or people who cannot program by themselves will get promoted anyway, the software will get more bugs and less features, and things will be generally worse for both consumers and programmers... but I prefer not to think about this option)\n(Well, either that or people who cannot program by themselves will get promoted anyway, the software will get more bugs and less features, and things will be generally worse for both consumers and programmers... but I prefer not to think about this option)\n\n\n\n\n\n\nIs that better?\n\n\n\n\n\n\nNot sure how it can be read otherwise.\n\n\n\nThat doesn't make any sense.\n\n\n\n\n\n\nBut the actual software part? I'm not sure anymore\n\n\n\nI feel the same way today, but I got started around 2012 professionally. I wonder how much of this is just our fading optimism after seeing how shit really works behind the scenes, and how much the industry itself is responsible for it. I know we're not the only two people feeling this way either, but it seems all of us have different timescales from when it turned from \"enjoyable\" to \"get me out of here\".\n\n\n\nThen one day I woke up and realized the ones paying me were also the ones using it to run over or do circles around everyone else not equipped with a bicycle yet; and were colluding to make crippled bicycles that'd never liberate the masses as much as they themselves had been previously liberated; bicycles designed to monitor, or to undermine their owner, or more disgustingly, their \"licensee\".So I'm not doing it anymore. I'm not going to continue making deliberately crippled, overly complex, legally encumbered bicycles for the mind, purely intended as subjects for ARR extraction.\nSo I'm not doing it anymore. I'm not going to continue making deliberately crippled, overly complex, legally encumbered bicycles for the mind, purely intended as subjects for ARR extraction.\n\n\n\n\n\n\nThis is also shocking to me. Especially here on HN! Every tech CEO on earth is salivating over AI coding because they want it to devalue and/or replace their expensive human software developers. Whether or not that will actually happen, that's the purpose of building all of these \"agentic\" coding tools. And here we are, dumbass software engineers, cheerleading for and building the means of our own destruction! We downplay it with bullshit like \"Oh, but AI is just a way to augment our work, it will never really replace us or lower our compensation!\" Wild how excited we all are about this.\n\n\n\n\n\n\n\n\n\n\n\n\nthis website is owned and operated by a VC, who build fortunes off exploiting these people\"workers and oppressed peoples of all countries, unite!\" is the last thing I'd expect to see here\n\"workers and oppressed peoples of all countries, unite!\" is the last thing I'd expect to see here\n\n\n\nAnybody who thinks this place represents the average working or middle class programmer hasn't been paying much attention. They fool a lot of people by being social liberal to go along with their economic liberalism.\n\n\n\n\n\n\nWe should not forget that on the other side of this issue are equally smart and motivated people and they too are aware of the power dynamics involved. For example, the phenomena of younger programmers poo pooing experienced engineers was a completely new valuation paradigm pushed by interested parties at some point around the dotcom bubble.Doctors with n years in the OR will not take shit from some intern that just came out of school. But we were placed in that situation at some point after '00. So the fundamental issue is that there is an (engineered imho) generational divide, and coupled with age discrimination in hiring (again due to interested parties' incentives) has a created a situation where one side is accumiliating generational wealth and power and the other side (us developers) are divided by age and the ones with the most skin in the game are naive youngsters who have no clue and have been taught to hate on \"millenials\" and \"old timers\" etc.\nDoctors with n years in the OR will not take shit from some intern that just came out of school. But we were placed in that situation at some point after '00. So the fundamental issue is that there is an (engineered imho) generational divide, and coupled with age discrimination in hiring (again due to interested parties' incentives) has a created a situation where one side is accumiliating generational wealth and power and the other side (us developers) are divided by age and the ones with the most skin in the game are naive youngsters who have no clue and have been taught to hate on \"millenials\" and \"old timers\" etc.\n\n\n\n    > Because other professional fields have not been subjected to a long running effort to commoditize software engineers.\n\nIn the United States, aren't Nurse Practitioner and Physician Assistant a \"direct assault\" on medical doctors?  I assume these roles were created in a pushback at the expense of medical doctors.    > And further, most other (cognitive) professionals are not subject to 'age shaming' and discounting of experience.\n\nI am of two minds about this comment.  TL;DR: \"Yeah, but...\"  One thing that I have noticed in my career: Most people can pump out much more code and work longer hours when they are young.  Then, when they get a bit older and/or start a family (and usually want better work/life balance), they start to play the \"experience\" card, which rarely translates into higher realised economic productivity.  Yes, most young devs write crap code, but they can write a lot of it.  If you can find good young devs, they are way cheaper and faster than experience devs.  I write that sentence with the controversial view that most businesses don't need amazing/perfect software; they just need \"good enough\" (which talented juniors can more than provide).When young people learn that I am a software developer, their eyes light up (thinking that I make huge money working for FAANG).  Frequently, they ask if they should also become a software developer.  I tell them no, because this industry requires constant self-learning that is very hard to sustain after 40.  Then, you become a target for layoffs, and getting re-employed after 40 as a software dev can be very tough.\n    > And further, most other (cognitive) professionals are not subject to 'age shaming' and discounting of experience.\n\nI am of two minds about this comment.  TL;DR: \"Yeah, but...\"  One thing that I have noticed in my career: Most people can pump out much more code and work longer hours when they are young.  Then, when they get a bit older and/or start a family (and usually want better work/life balance), they start to play the \"experience\" card, which rarely translates into higher realised economic productivity.  Yes, most young devs write crap code, but they can write a lot of it.  If you can find good young devs, they are way cheaper and faster than experience devs.  I write that sentence with the controversial view that most businesses don't need amazing/perfect software; they just need \"good enough\" (which talented juniors can more than provide).When young people learn that I am a software developer, their eyes light up (thinking that I make huge money working for FAANG).  Frequently, they ask if they should also become a software developer.  I tell them no, because this industry requires constant self-learning that is very hard to sustain after 40.  Then, you become a target for layoffs, and getting re-employed after 40 as a software dev can be very tough.\nWhen young people learn that I am a software developer, their eyes light up (thinking that I make huge money working for FAANG).  Frequently, they ask if they should also become a software developer.  I tell them no, because this industry requires constant self-learning that is very hard to sustain after 40.  Then, you become a target for layoffs, and getting re-employed after 40 as a software dev can be very tough.\n\n\n\nSo nothing new? Just this/last month, it seems like the multi-select \"open/close\" button in the GitHub PR UI was just straight up broken. No one seemed to have noticed until I opened a bug report, and it continued being broken for weeks before they finally fixed it. Not the first time I encounter this on Microsoft properties, they seem to constantly push out broken shit, and no one seem to even notice until some sad user (like me) happens to stumble across it.\n\n\n\nI am speculating that this \"AI Revolution\" may lead to some revitalization of the movement as it would allow individual contributors the ability to compete on the same levels as proprietary software providers who previously had to employ legions of developers to create their software.\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nSo, for experienced engineers, I see a great future fixing the shit show that is AI-code.\n\n\n\n\n\n\nBecause that shit makes you insane as well.\n\n\n\n\n\n\n\n\n\n\n\n\nThank you. It's something I'm actively pursuing, I'm hoping to finish some chairs this spring and see if any local shops are interested in stocking them. But I'm skeptical I could find enough business to make it work full-time, pay for my family's health insurance, and so on.  We'll see.\n\n\n\n\n\n\n\n\n\n\n\n\nAt what point does the human developers just give up and close the PRs as \"AI garbage\". Keep the ones that works, then just junk the rest. I feel that at some point entertaining the machine becomes unbearable and people just stops doing it or rage close the PRs.\n\n\n\nMicrosoft's stock price is dependent on them proving that this is a success.\n\n\n\nPerhaps this explains the recent firings that affected faster CPython and other projects. While they throw money at AI but sucess still doesn't materialize, they need to make the books look good for yet another quarter through the old-school reliable method of laying off people left and right.\n\n\n\n\n\n\nit's not as if Microsoft's share price has ever reflected the quality of their products\n\n\n\nAt one point, their desktop user experience was actually pretty good. And that was all their products back then. They definitely didn't get to where they are now by selling products that were bad.   You could make the argument that some of them were bad but they were cheap, but if price is a big aspect of what makes a product good in the eyes of the consumer at the time and nobody else is competing on price, then that isn't \"bad\" in the sense I'm using the word.I don't think I'd have called them out for always making terrible products all the way through till about Windows 7. I had no major complaints about that release, cloud was in its infancy, no pushing 365 etc. After that, quality started to go downhill. To the point that I'd argue with a straight face that most major community supported Linux DEs provide an objectively better and more stable user experience for both technical and non technical users.\nI don't think I'd have called them out for always making terrible products all the way through till about Windows 7. I had no major complaints about that release, cloud was in its infancy, no pushing 365 etc. After that, quality started to go downhill. To the point that I'd argue with a straight face that most major community supported Linux DEs provide an objectively better and more stable user experience for both technical and non technical users.\n\n\n\n\n\n\nNo need to specify why they are interact with it, all engagement is good engagement.\n\n\n\n\n\n\nMost corporate BS comes down to this.\n\n\n\n\n\n\n    > rage close the PRs\n\nI am shaking with laughter reading this phrase.  You got me good here.  It is the perfect repurpose of \"rage quit\" for the AI slop era.  I hope that we see some MSFT employees go insane from responding to so many shitty PRs from LLMs.One of my all time \"rage quit\" stories is Azer Koçulu of npm left-pad incident infamy.  That guy is my Internet hero -- \"fight the power\".\nOne of my all time \"rage quit\" stories is Azer Koçulu of npm left-pad incident infamy.  That guy is my Internet hero -- \"fight the power\".\n\n\n\n\n\n\nThe feedback buttons open a feedback form modal, they don’t reflect the number of feedback given like the emoji button. If you leave feedback, it will reflect your thumbs up/down (hiding the other button), it doesn’t say anything about whether anyone else has left feedback (I’ve tried it on my own repos).\n\n\n\nComment in the GitHub discussion:\"...You and I and every programmer who hasn't been living under a rock knows that AI isn't ready to be adopted at this scale yet, on the premier; 100M-user code-hosting platform. It doesn't make any sense except in brain-washed corporate-talk like \"we are testing today what it can do tomorrow\".I'm not saying that this couldn't be an adequate change some day, perhaps even in a few years but we all know this isn't it today. It's 100% financial-driven hype with a pinch of we're too big to fail mentality...\"\n\"...You and I and every programmer who hasn't been living under a rock knows that AI isn't ready to be adopted at this scale yet, on the premier; 100M-user code-hosting platform. It doesn't make any sense except in brain-washed corporate-talk like \"we are testing today what it can do tomorrow\".I'm not saying that this couldn't be an adequate change some day, perhaps even in a few years but we all know this isn't it today. It's 100% financial-driven hype with a pinch of we're too big to fail mentality...\"\nI'm not saying that this couldn't be an adequate change some day, perhaps even in a few years but we all know this isn't it today. It's 100% financial-driven hype with a pinch of we're too big to fail mentality...\"\n\n\n\nIt's all just recycled rent seeking corporate hype for enterprise compute.The moment I had decided to learn Kubernetes years ago, got a book and saw microservices compared to 'object-oriented' programming I realized that. The 'big ball of mud' paper and the 'worse is better' rant frame it all pretty well in my view. Prioritize velocity, get slop in production, cope with the accidental complexity, rinse repeat. Eventually you get to a point where GPU farms seem like a reasonable way to auto-complete code.When you find yourself in a hole, stop digging. Any bigger excavator you send down there will only get buried when the mud crashes down.\nThe moment I had decided to learn Kubernetes years ago, got a book and saw microservices compared to 'object-oriented' programming I realized that. The 'big ball of mud' paper and the 'worse is better' rant frame it all pretty well in my view. Prioritize velocity, get slop in production, cope with the accidental complexity, rinse repeat. Eventually you get to a point where GPU farms seem like a reasonable way to auto-complete code.When you find yourself in a hole, stop digging. Any bigger excavator you send down there will only get buried when the mud crashes down.\nWhen you find yourself in a hole, stop digging. Any bigger excavator you send down there will only get buried when the mud crashes down.\n\n\n\nWhy do they even need it? Success is code getting merged 1st shot, failure gets worse the more requests for changes the agent gets. Asking for manual feedback seems like a waste of time. Measure cycle time and rate of approvals and change failure rate like you would for any developer.\n\n\n\nAnyone who has dealt with Microsoft support knows this feeling well. Even talking to the higher level customer success folks feels like talking to a brick wall. After dozens of support cases, I can count on zero hands the number of issues that were closed satisfactorily.I appreciate Microsoft eating their dogfood here, but please don't make me eat it too! If anyone from MS is reading this, please release finished products that you are prepared to support!\nI appreciate Microsoft eating their dogfood here, but please don't make me eat it too! If anyone from MS is reading this, please release finished products that you are prepared to support!\n\n\n\nTypically, you wouldn't bother manually reviewing something until the automated checks have passed.\n\n\n\n\n\n\nLet them finish a pull request before spending time reviewing it. That said, a merge request needs to have an issue written before it's picked up, so that the author does not spend time on a solution before the problem is understood. That's idealism though.\n\n\n\nI'd rather hop in and get them on the right path rather than letting them struggle alone, particularly if they're struggling.If it's another senior developer though I'd happily leave them to it to get the unit tests all passing before I take a proper look at their work.But as a general principle, please at least get a PR through formatting checks before assigning it to a person.\nIf it's another senior developer though I'd happily leave them to it to get the unit tests all passing before I take a proper look at their work.But as a general principle, please at least get a PR through formatting checks before assigning it to a person.\nBut as a general principle, please at least get a PR through formatting checks before assigning it to a person.\n\n\n\nThe earliest feedback you can get comes from the compiler. If it won't build successfully don't submit the PR.\n\n\n\nhttps://github.com/dotnet/runtime/pull/115732#issuecomment-2...\n\n\n\nMaybe, but likely it is reality and their true company culture leaking through. Eventually some higher eq execs might come to the very late realization that they cant actually lead or build a worthwhile and productive company culture and all that remains is an insane reflection of that.\n\n\n\nI agree that not auto-collapsing repeated annotations is an annoying bug in the github interface.But just pointing out that annotations can be hidden in the ... menu to the right (which I just learned).\nBut just pointing out that annotations can be hidden in the ... menu to the right (which I just learned).\n\n\n\n\n\n\n\n\n\nAnd then, while the tech is not mature, running on delusion and sunken costs, it's actually used for production stuffs. Butlerian Jihad when\n\n\n\nMy sophisticated sentiment analysis (talking to co-workers other professional programmers and IT workers, HN and Reddit comments) seems to indicate a shift--there's a lot less storybook \"Ay Eye is gonna take over the world\" talk and a lot more distrust and even disdain than you'd see even 6 months ago.Moves like this will not go over well.\nMoves like this will not go over well.\n\n\n\n\n\n\nI estimate two more years for the bubble to pop.\n\n\n\n\n\n\n\n\n\nWhich will soon be anyone who directly or indirectly relies on Microsoft technologies. Some of these PRs, including at least one that I saw reworked certificate validation logic with not much more than a perfunctory “LGTM”, have been merged into main.Coincidentally, I wonder if issues orthogonal to this slop is why I’ve been getting so many HTTP 500 errors when using GitHub lately.\nCoincidentally, I wonder if issues orthogonal to this slop is why I’ve been getting so many HTTP 500 errors when using GitHub lately.\n\n\n\n> The stream of PRs is coming from requests from the maintainers of the repo. We're experimenting to understand the limits of what the tools can do today and preparing for what they'll be able to do tomorrow. Anything that gets merged is the responsibility of the maintainers, as is the case for any PR submitted by anyone to this open source and welcoming repo. Nothing gets merged without it meeting all the same quality bars and with us signing up for all the same maintenance requirements.\n\n\n\n> It is my opinion that anyone not at least thinking about benefiting from such tools will be left behind.The read here is: Microsoft is so abuzz with excitement/panic about AI taking all software engineering jobs that Microsoft employees are jumping on board with Microsoft's AI push out of a fear of \"being left behind\". That's not the confidence inspiring the statement they intended it to be, it's the opposite, it underscores that this isn't the .net team \"experimenting to understand the limits of what the tools\" but rather the .net team trying to keep their jobs.\nThe read here is: Microsoft is so abuzz with excitement/panic about AI taking all software engineering jobs that Microsoft employees are jumping on board with Microsoft's AI push out of a fear of \"being left behind\". That's not the confidence inspiring the statement they intended it to be, it's the opposite, it underscores that this isn't the .net team \"experimenting to understand the limits of what the tools\" but rather the .net team trying to keep their jobs.\n\n\n\nLike, I need to start smashing my face into a keyboard for 10000 hours or else I won't be able to use LLM tools effectively.If LLM is this tool that is more intuitive than normal programming and adds all this productivity, then surely I can just wait for a bunch of others to wear themselves out smashing the faces on a keyboard for 10000 hours and then skim the cream off of the top, no worse for wear.On the other hand, if using LLMs is a neverending nightmare of chaos and misery that's 10x harder than programming (but with the benefit that I don't actually have to learn something that might accidentally be useful), then yeah I guess I can see why I would need to get in my hours to use it.  But maybe I could just not use it.\"Left behind\" really only makes sense to me if my KPIs have been linked with LLM flavor aid style participation.Ultimately, though, physics doesn't care about social conformity and last I checked the machine is running on physics.\nIf LLM is this tool that is more intuitive than normal programming and adds all this productivity, then surely I can just wait for a bunch of others to wear themselves out smashing the faces on a keyboard for 10000 hours and then skim the cream off of the top, no worse for wear.On the other hand, if using LLMs is a neverending nightmare of chaos and misery that's 10x harder than programming (but with the benefit that I don't actually have to learn something that might accidentally be useful), then yeah I guess I can see why I would need to get in my hours to use it.  But maybe I could just not use it.\"Left behind\" really only makes sense to me if my KPIs have been linked with LLM flavor aid style participation.Ultimately, though, physics doesn't care about social conformity and last I checked the machine is running on physics.\nOn the other hand, if using LLMs is a neverending nightmare of chaos and misery that's 10x harder than programming (but with the benefit that I don't actually have to learn something that might accidentally be useful), then yeah I guess I can see why I would need to get in my hours to use it.  But maybe I could just not use it.\"Left behind\" really only makes sense to me if my KPIs have been linked with LLM flavor aid style participation.Ultimately, though, physics doesn't care about social conformity and last I checked the machine is running on physics.\n\"Left behind\" really only makes sense to me if my KPIs have been linked with LLM flavor aid style participation.Ultimately, though, physics doesn't care about social conformity and last I checked the machine is running on physics.\nUltimately, though, physics doesn't care about social conformity and last I checked the machine is running on physics.\n\n\n\nKinda like how word processing used to be an important career skill people put on their resumes. Assuming AI becomes as that commonplace and accessible, will it happen fast enough that devs who want good jobs can afford to just wait that out?\n\n\n\nIf LLM usage is easy then I can't be left behind because it's easy.  I'll pick it up in a weekend.If LLM usage is hard AND I can otherwise do the hard things that LLMs are doing then I can't be left behind if I just do the hard things.Still the only way I can be left behind is if LLM usage is nonsense or the same as just doing it yourself AND the important thing is telling managers that you've been using it for a long time.Is the superpower bamboozling management with story time?\nIf LLM usage is hard AND I can otherwise do the hard things that LLMs are doing then I can't be left behind if I just do the hard things.Still the only way I can be left behind is if LLM usage is nonsense or the same as just doing it yourself AND the important thing is telling managers that you've been using it for a long time.Is the superpower bamboozling management with story time?\nStill the only way I can be left behind is if LLM usage is nonsense or the same as just doing it yourself AND the important thing is telling managers that you've been using it for a long time.Is the superpower bamboozling management with story time?\nIs the superpower bamboozling management with story time?\n\n\n\n\n\n\nUnless we're talking about hard things that I have up til now not been able to do.  But do LLMs help with that in general?This scenario breaks out of the hypothetical and the assertive and into the realm of the testable.Provide for me the person who can use LLMs in a way that is hard but they are good at in order to do things which are hard but which they are currently bad at.I will provide a task which is hard.We can report back the result.\nThis scenario breaks out of the hypothetical and the assertive and into the realm of the testable.Provide for me the person who can use LLMs in a way that is hard but they are good at in order to do things which are hard but which they are currently bad at.I will provide a task which is hard.We can report back the result.\nProvide for me the person who can use LLMs in a way that is hard but they are good at in order to do things which are hard but which they are currently bad at.I will provide a task which is hard.We can report back the result.\nI will provide a task which is hard.We can report back the result.\nWe can report back the result.\n\n\n\nA PM using LLM to develop software product without DEV?\n\n\n\nAlso Im picking the problem. I have a few in mind but I would want to get the background of the person running the experiment first to ensure that the problem is something that we can expect to be hard for the person.\n\n\n\nLaw, civil service, academia and those who learnt enough LaTeX and HTML to understand text documents are in the minority.\n\n\n\n\n\n\nIt's like the 2025 version not not using an IDE.It's a powerful tool. You still need to know when to and when not to use it.\nIt's a powerful tool. You still need to know when to and when not to use it.\n\n\n\nThat's right on the mark. It will save you a little bit of work on tasks that aren't the bottleneck on your productivity, and disrupt some random tasks that may or may not be important.It's makes so little difference that plenty of people in 2025 don't use an IDE, and looking at their performance from the outside one just can't tell.Except that LLMs have less potential to improve your tasks and more potential to be disruptive.\nIt's makes so little difference that plenty of people in 2025 don't use an IDE, and looking at their performance from the outside one just can't tell.Except that LLMs have less potential to improve your tasks and more potential to be disruptive.\nExcept that LLMs have less potential to improve your tasks and more potential to be disruptive.\n\n\n\nEven for writing tests, you have to proof-read every single line and triple check they didn't write a broken test. It's absolutely exhausting.\n\n\n\n\n\n\nHow can it be that people expect that pumping more energy into closed systems could do anything else than raise entropy? Because that's what it is. You attach GPU farms to your code base and make them pump code into it? You're pumping energy into a closed system. The result cannot be other than greater entropy.\n\n\n\nThe reason LLMs fail so often are not related to the fundamental of \"garbage in, garbage out\".\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nI think, we should not read too much into it. He is honestly exploring how much this tool can help him to resolve trivial issues. Maybe he was asked to do so by some of his bosses, but unlikely to fear the tool replacing him in the near future.\n\n\n\nhttps://www.theregister.com/2025/05/16/microsofts_axe_softwa...Perhaps they were fired for failing to show enthusiasm for AI?\nPerhaps they were fired for failing to show enthusiasm for AI?\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nHalf of Microsoft (especially server-side) still runs on dotnet. And there are no real contributors outside of microsoft. So it is a vital project.\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nIf they weren't experimenting with AI and coding and took a more conservative approach, while other companies like Anthropic was running similar experiments, I'm sure HN would also be critiquing them for not keeping up as a stodgy big corporation.As long as they are willing to take risks by trying and failing on their own repos, it's fine in my books. Even though I'd never let that stuff touch a professional github repo personally.\nAs long as they are willing to take risks by trying and failing on their own repos, it's fine in my books. Even though I'd never let that stuff touch a professional github repo personally.\n\n\n\n\n\n\nAt the moment, I'd arguing doing much more than what say Apple is doing would be what is potentially catastrophic. Not doing anything would be minimally risky, and doing just a little bit would be the no risk play. I think Microsoft is making this mistake in a big way and will continue to lose market share over it and burn cash, albeit slowly since they are already giants. The point is, it's a giant that has momentum going in the opposite direction than what they want, and they are incapable of fixing the things causing it to go in that direction because their leadership has become delusional.\n\n\n\n\n\n\n\n\n\nIn my org, we would have had to bypass precommit hooks to do this!\n\n\n\n\n\n\nI see this as a work in progress..   I am almost certain the humans in the loop on these PRs are well aware of what's going on and have their expectations in check, and this isn't just \"business as usual\" like any other PR or work assignment.This is a test.  You can't improve a system without testing it on real world conditions.How do we know they're not tweaking the Copilot system prompts and settings behind the scenes while they're doing this work?Can no one see the possibility that what is happening in those PRs is exactly what all the people involved expected to have happen, and they're just going through the process of seeing what happens when you try to refine and coach the system to either success or failure?When we adopted AI coding assist tools internally over a year ago we did almost exactly this (not directly in GitHub though).We asked a bunch of senior engineers to see how far they could get by coaching the AI to write code rather than writing it themselves.  We wanted to calibrate our expectations and better understand the limits, strengths and weaknesses of these new tools we wanted to adopt.In most of those early cases we ended up with worse code than if it had been written by humans, but we learned a ton.   We can also clearly see how much better things have gotten over time, since we have that benchmark to look back on.\nThis is a test.  You can't improve a system without testing it on real world conditions.How do we know they're not tweaking the Copilot system prompts and settings behind the scenes while they're doing this work?Can no one see the possibility that what is happening in those PRs is exactly what all the people involved expected to have happen, and they're just going through the process of seeing what happens when you try to refine and coach the system to either success or failure?When we adopted AI coding assist tools internally over a year ago we did almost exactly this (not directly in GitHub though).We asked a bunch of senior engineers to see how far they could get by coaching the AI to write code rather than writing it themselves.  We wanted to calibrate our expectations and better understand the limits, strengths and weaknesses of these new tools we wanted to adopt.In most of those early cases we ended up with worse code than if it had been written by humans, but we learned a ton.   We can also clearly see how much better things have gotten over time, since we have that benchmark to look back on.\nHow do we know they're not tweaking the Copilot system prompts and settings behind the scenes while they're doing this work?Can no one see the possibility that what is happening in those PRs is exactly what all the people involved expected to have happen, and they're just going through the process of seeing what happens when you try to refine and coach the system to either success or failure?When we adopted AI coding assist tools internally over a year ago we did almost exactly this (not directly in GitHub though).We asked a bunch of senior engineers to see how far they could get by coaching the AI to write code rather than writing it themselves.  We wanted to calibrate our expectations and better understand the limits, strengths and weaknesses of these new tools we wanted to adopt.In most of those early cases we ended up with worse code than if it had been written by humans, but we learned a ton.   We can also clearly see how much better things have gotten over time, since we have that benchmark to look back on.\nCan no one see the possibility that what is happening in those PRs is exactly what all the people involved expected to have happen, and they're just going through the process of seeing what happens when you try to refine and coach the system to either success or failure?When we adopted AI coding assist tools internally over a year ago we did almost exactly this (not directly in GitHub though).We asked a bunch of senior engineers to see how far they could get by coaching the AI to write code rather than writing it themselves.  We wanted to calibrate our expectations and better understand the limits, strengths and weaknesses of these new tools we wanted to adopt.In most of those early cases we ended up with worse code than if it had been written by humans, but we learned a ton.   We can also clearly see how much better things have gotten over time, since we have that benchmark to look back on.\nWhen we adopted AI coding assist tools internally over a year ago we did almost exactly this (not directly in GitHub though).We asked a bunch of senior engineers to see how far they could get by coaching the AI to write code rather than writing it themselves.  We wanted to calibrate our expectations and better understand the limits, strengths and weaknesses of these new tools we wanted to adopt.In most of those early cases we ended up with worse code than if it had been written by humans, but we learned a ton.   We can also clearly see how much better things have gotten over time, since we have that benchmark to look back on.\nWe asked a bunch of senior engineers to see how far they could get by coaching the AI to write code rather than writing it themselves.  We wanted to calibrate our expectations and better understand the limits, strengths and weaknesses of these new tools we wanted to adopt.In most of those early cases we ended up with worse code than if it had been written by humans, but we learned a ton.   We can also clearly see how much better things have gotten over time, since we have that benchmark to look back on.\nIn most of those early cases we ended up with worse code than if it had been written by humans, but we learned a ton.   We can also clearly see how much better things have gotten over time, since we have that benchmark to look back on.\n\n\n\n\n\n\n\n\n\n\n\n\nredbull does not give you wings. it’s disconcerting to see the lack of nuance in these discussions around these new tools (and yeah sorry this isn’t really aimed at you, but the zeitgeist, apologies)\n\n\n\nSome of us are being laid off due to the hype; some are assigned to babysit the AI; and some are simply looked down on by higher ups who are eagerly waiting for a day to lay us all off.You can convince yourself as much as you want that it’s “just a hype”, but regardless of your beliefs are, it has REAL world consequences.\nYou can convince yourself as much as you want that it’s “just a hype”, but regardless of your beliefs are, it has REAL world consequences.\n\n\n\nengineers are testing promising new technology. a mob (of probably half or more bots) is having a [redacted] perpetuating the anti-narrative they huffed themselves up into believing. and now we’re in a meta-[redacted] as if either A) redditors and armchair engineers here have valid opinions on this tech and B) marketers and founders with massive incentives to overpromise are telling a true narrativewhy? we don’t have to do it. we could actually look at these topics with nuance and not react like literal bots to everything(sorry I’m just losing my faith in humanity and taking it out in this thread)\nwhy? we don’t have to do it. we could actually look at these topics with nuance and not react like literal bots to everything(sorry I’m just losing my faith in humanity and taking it out in this thread)\n(sorry I’m just losing my faith in humanity and taking it out in this thread)\n\n\n\nbecause it is more than marketing hype. real people are taking real action based on this narrative.> why do you actually accept this is a real narrative real people believe?largely because I witness real people believing this narrative with my own eyes on a daily basis.\n> why do you actually accept this is a real narrative real people believe?largely because I witness real people believing this narrative with my own eyes on a daily basis.\nlargely because I witness real people believing this narrative with my own eyes on a daily basis.\n\n\n\nBecause we're literally seeing people being laid off with narratives about being replaced with AI (At a whole slew of companies).  Because we're seeing company policies around hiring being changed to require hiring managers to provide exhaustive justifications why the work couldn't be handled by an AI (at e.g. Shopify, Salesforce and so on)> have you talked to the executives implementing these strategies?I have had a few conversations, yes.  Have you?  They're weirdly \"true believers\" that are buying the marketing hype hook line and sinker.  They're doing small coding exercises themselves in these tools, seeing that they as an executive can manage to get valid code for the small exercise out the other side of it, and assuming that that means it can replace head count.  Either deliberately or naively failing to understand that there is a world of difference between leet code style exercises, or quick small changes to code bases, and actual software development.The weirdest conversation recently, which thankfully I got to just be on the periphery of, involved an engineering org that decided to try to replace the post-incident process with one entirely written by LLMs.  It would take timelines from a ticket, and a small prompt to write up the entire post-incident report, tasks etc.The whole project showed a gross misunderstanding of the point of post-incident stuff, eradicating \"introspection\" and \"learning from your mistakes\", turning it into a check box exercise for teams.  Even their narrative around what they were doing was hilarious, because it came down to \"Get the post-incident report out of the way so we can concentrate on the real work\".\n> have you talked to the executives implementing these strategies?I have had a few conversations, yes.  Have you?  They're weirdly \"true believers\" that are buying the marketing hype hook line and sinker.  They're doing small coding exercises themselves in these tools, seeing that they as an executive can manage to get valid code for the small exercise out the other side of it, and assuming that that means it can replace head count.  Either deliberately or naively failing to understand that there is a world of difference between leet code style exercises, or quick small changes to code bases, and actual software development.The weirdest conversation recently, which thankfully I got to just be on the periphery of, involved an engineering org that decided to try to replace the post-incident process with one entirely written by LLMs.  It would take timelines from a ticket, and a small prompt to write up the entire post-incident report, tasks etc.The whole project showed a gross misunderstanding of the point of post-incident stuff, eradicating \"introspection\" and \"learning from your mistakes\", turning it into a check box exercise for teams.  Even their narrative around what they were doing was hilarious, because it came down to \"Get the post-incident report out of the way so we can concentrate on the real work\".\nI have had a few conversations, yes.  Have you?  They're weirdly \"true believers\" that are buying the marketing hype hook line and sinker.  They're doing small coding exercises themselves in these tools, seeing that they as an executive can manage to get valid code for the small exercise out the other side of it, and assuming that that means it can replace head count.  Either deliberately or naively failing to understand that there is a world of difference between leet code style exercises, or quick small changes to code bases, and actual software development.The weirdest conversation recently, which thankfully I got to just be on the periphery of, involved an engineering org that decided to try to replace the post-incident process with one entirely written by LLMs.  It would take timelines from a ticket, and a small prompt to write up the entire post-incident report, tasks etc.The whole project showed a gross misunderstanding of the point of post-incident stuff, eradicating \"introspection\" and \"learning from your mistakes\", turning it into a check box exercise for teams.  Even their narrative around what they were doing was hilarious, because it came down to \"Get the post-incident report out of the way so we can concentrate on the real work\".\nThe weirdest conversation recently, which thankfully I got to just be on the periphery of, involved an engineering org that decided to try to replace the post-incident process with one entirely written by LLMs.  It would take timelines from a ticket, and a small prompt to write up the entire post-incident report, tasks etc.The whole project showed a gross misunderstanding of the point of post-incident stuff, eradicating \"introspection\" and \"learning from your mistakes\", turning it into a check box exercise for teams.  Even their narrative around what they were doing was hilarious, because it came down to \"Get the post-incident report out of the way so we can concentrate on the real work\".\nThe whole project showed a gross misunderstanding of the point of post-incident stuff, eradicating \"introspection\" and \"learning from your mistakes\", turning it into a check box exercise for teams.  Even their narrative around what they were doing was hilarious, because it came down to \"Get the post-incident report out of the way so we can concentrate on the real work\".\n\n\n\nGiven how often leet code questions are used in the interview process across the entire industry I think it’s a fair assumption that they fail to understand this.\n\n\n\n>> This is a test. You can't improve a system without testing it on real world conditions.Software developers know to fix build problems before asking for a review. The AIs are submitting PRs in bad faith because they don't know any better. Compilers and other build tools produce errors when they fail, and the AI is ignoring this first line of feedback.It is not a maintainers job to review code for syntax errors, or use of APIs that don't actually exist, or other silly mistakes. That's the compilers job and it does it well. The AI needs to take that feedback and fix the issues before escalating to humans.\nSoftware developers know to fix build problems before asking for a review. The AIs are submitting PRs in bad faith because they don't know any better. Compilers and other build tools produce errors when they fail, and the AI is ignoring this first line of feedback.It is not a maintainers job to review code for syntax errors, or use of APIs that don't actually exist, or other silly mistakes. That's the compilers job and it does it well. The AI needs to take that feedback and fix the issues before escalating to humans.\nIt is not a maintainers job to review code for syntax errors, or use of APIs that don't actually exist, or other silly mistakes. That's the compilers job and it does it well. The AI needs to take that feedback and fix the issues before escalating to humans.\n\n\n\n\n\n\n\n\n\nIt's going to look stupid... until the point it doesn't. And my money's on, \"This will eventually be a solved problem.\"\n\n\n\nGood decision making would weigh the odds of 1 vs 8 vs 16 years. This isn’t good decision making.\n\n\n\n\n\n\nWhy is doing a public test of an emerging technology not good decision making?> Good decision making would weigh the odds of 1 vs 8 vs 16 years.What makes you think this isn't being done?\n> Good decision making would weigh the odds of 1 vs 8 vs 16 years.What makes you think this isn't being done?\nWhat makes you think this isn't being done?\n\n\n\nAI can remain stupid longer than you can remain solvent.\n\n\n\nMy variation was:\"Leadership can stay irrational longer than you can stay employed\"\n\"Leadership can stay irrational longer than you can stay employed\"\n\n\n\nI'm not so sure they'll get there. If the solved problem is defined as a sub-standard but low cost, then I wouldn't bet against that. A solution better than that though, I don't think I'd put my money on that.\n\n\n\nWhat if the goalpost is shifted backwards, to the 90% mark (instead of demanding that AI get to 100%)?* Big corps could redefine \"good enough\" as \"what the SotA AI can do\" and call it good.* They could then layoff even more employees, since the AI would be, by definition, Good Enough.(This isn't too far-fetched, IMO, seeing how we're seeing calls for copyright violation to be classified as legal-when-we-do-it)\n* Big corps could redefine \"good enough\" as \"what the SotA AI can do\" and call it good.* They could then layoff even more employees, since the AI would be, by definition, Good Enough.(This isn't too far-fetched, IMO, seeing how we're seeing calls for copyright violation to be classified as legal-when-we-do-it)\n* They could then layoff even more employees, since the AI would be, by definition, Good Enough.(This isn't too far-fetched, IMO, seeing how we're seeing calls for copyright violation to be classified as legal-when-we-do-it)\n(This isn't too far-fetched, IMO, seeing how we're seeing calls for copyright violation to be classified as legal-when-we-do-it)\n\n\n\n\n\n\nI have met people who believe that automobile engineering peaked in the 1960's, and they will argue that until you are blue in the face.\n\n\n\n\n\n\nSo the typical expectations or norms of how code reviews and PRs work between humans don't really apply here.That's my guess at least.  I have no more insider information than you.\nThat's my guess at least.  I have no more insider information than you.\n\n\n\nEVERY single prompt should have the opportunity to get copied off into a permanent log where the end user triggers it : log all input, all output, human writes a summary of what he wanted to happen but did not, what he thinks might have went wrong, what he thinks should have happened (domain specific experts giving feedback about how things are fucking up) And then its still only useful with long term tracking like how someone actually made a training change to fix this exact failure scenario.None of that exists, so just like \"full self driving\" was a pie in the sky bullshit dream that proved machine learning has an 80/20 never gonna fully work problem, same thing here\nNone of that exists, so just like \"full self driving\" was a pie in the sky bullshit dream that proved machine learning has an 80/20 never gonna fully work problem, same thing here\n\n\n\nUnfortunately, just about every thread on this genre is like that now.\n\n\n\nOtherwise it would check the tests are passing.\n\n\n\n1. Working out in the open2. Dogfooding their own product3. Pushing the state of the artGiven that the negative impact here falls mostly (completely?) on the Microsoft team which opted into this, is there any reason why we shouldn't be supporting progress here?\n2. Dogfooding their own product3. Pushing the state of the artGiven that the negative impact here falls mostly (completely?) on the Microsoft team which opted into this, is there any reason why we shouldn't be supporting progress here?\n3. Pushing the state of the artGiven that the negative impact here falls mostly (completely?) on the Microsoft team which opted into this, is there any reason why we shouldn't be supporting progress here?\nGiven that the negative impact here falls mostly (completely?) on the Microsoft team which opted into this, is there any reason why we shouldn't be supporting progress here?\n\n\n\nIt’s showing the actual capabilities in practice. That’s much better and way more illuminating than what normally happens with sales and marketing hype.\n\n\n\nZuckerberg says: \"Our bet is sort of that in the next year probably … maybe half the development is going to be done by AI, as opposed to people, and then that will just kind of increase from there\".It's hard to square those statements up with what we're seeing happen on these PRs.\nIt's hard to square those statements up with what we're seeing happen on these PRs.\n\n\n\n\n\n\n\n\n\n\n\n\nIt is about as unethical as it gets.But, our current iteration of capitalism is highly financialized and underinvested in the value of engineering. Stock prices come before truth.\nBut, our current iteration of capitalism is highly financialized and underinvested in the value of engineering. Stock prices come before truth.\n\n\n\nWell, that makes sense to me. Microsoft's software has gotten noticably worse in the last few years. So much that I have abandoned it for my daily driver for the first time since the early 2000s.\n\n\n\n\n\n\nPersonally I just think it is funny that MS is soft launching a product into total failure.\n\n\n\n\n\n\n\n\n\nThis is what's happening right now: they are having to review every single line produced by this machine and trying to understand why it wrote what it wrote.Even with experienced developers reviewing and lots of tests, the likelihood of bugs in this code compared to a real engineer working on it is much higher.Why not do this on less mission critical software at the very least?Right now I'm very happy I don't write anything on .NET if this is what they'll use as a guinea pig for the snake oil.\nEven with experienced developers reviewing and lots of tests, the likelihood of bugs in this code compared to a real engineer working on it is much higher.Why not do this on less mission critical software at the very least?Right now I'm very happy I don't write anything on .NET if this is what they'll use as a guinea pig for the snake oil.\nWhy not do this on less mission critical software at the very least?Right now I'm very happy I don't write anything on .NET if this is what they'll use as a guinea pig for the snake oil.\nRight now I'm very happy I don't write anything on .NET if this is what they'll use as a guinea pig for the snake oil.\n\n\n\nI doubt that anyone expected to merge any of these PRs. Question is - can the machine solve minor (but non-trivial) issues listed on github in an efficient way with minimal guidance. Current answer is no.Also, _if_ anything was to be merged, dotnet is dogfooded extensively at Microsoft, so bugs in it are much more likely to be noticed and fixed before you get a stable release on your plate.\nAlso, _if_ anything was to be merged, dotnet is dogfooded extensively at Microsoft, so bugs in it are much more likely to be noticed and fixed before you get a stable release on your plate.\n\n\n\nIf it can't even make a decent commit into software nobody uses, how can it ever do it for something even more complex?\nAnd no, you don't need to review it with an intern...> can the machine solve minor (but non-trivial) issues listed on github in an efficient way with minimal guidanceI'm sorry but the only way this is even a question is if you never used AI in the real world.\nAnyone with a modicum of common sense would tell you immediately: it cannot.You can't even keep it \"sane\" in a small conversation, let alone using tons of context to accomplish non-trivial tasks.\n> can the machine solve minor (but non-trivial) issues listed on github in an efficient way with minimal guidanceI'm sorry but the only way this is even a question is if you never used AI in the real world.\nAnyone with a modicum of common sense would tell you immediately: it cannot.You can't even keep it \"sane\" in a small conversation, let alone using tons of context to accomplish non-trivial tasks.\nI'm sorry but the only way this is even a question is if you never used AI in the real world.\nAnyone with a modicum of common sense would tell you immediately: it cannot.You can't even keep it \"sane\" in a small conversation, let alone using tons of context to accomplish non-trivial tasks.\nYou can't even keep it \"sane\" in a small conversation, let alone using tons of context to accomplish non-trivial tasks.\n\n\n\nThis presupposes AI IS progress.Nevermind that what this actually shows is an executive or engineering team that so buys their own hype that they didn't even try to run this locally and internally before blasting to the world that their system can't even ensure tests are passing before submitting a PR. They are having a problem with firewall rules blocking the system from seeing CI outcomes and that's part of why it's doing so badly, so why wasn't that verified BEFORE doing this on stage?\"Working out in the open\" here is a bad thing. These are issues that SHOULD have been caught by an internal POC FIRST. You don't publicly do bullshit.\"Dogfooding\" doesn't require throwing this at important infrastructure code. Does VS code not have small bugs that need fixing? Infrastructure should expect high standards.\"Pushing the state of the art\" is comedy. This is the state of the art? This is pushing the state of the art? How much money has been thrown into the fire for this result? How much did each of those PRs cost anyway?\nNevermind that what this actually shows is an executive or engineering team that so buys their own hype that they didn't even try to run this locally and internally before blasting to the world that their system can't even ensure tests are passing before submitting a PR. They are having a problem with firewall rules blocking the system from seeing CI outcomes and that's part of why it's doing so badly, so why wasn't that verified BEFORE doing this on stage?\"Working out in the open\" here is a bad thing. These are issues that SHOULD have been caught by an internal POC FIRST. You don't publicly do bullshit.\"Dogfooding\" doesn't require throwing this at important infrastructure code. Does VS code not have small bugs that need fixing? Infrastructure should expect high standards.\"Pushing the state of the art\" is comedy. This is the state of the art? This is pushing the state of the art? How much money has been thrown into the fire for this result? How much did each of those PRs cost anyway?\n\"Working out in the open\" here is a bad thing. These are issues that SHOULD have been caught by an internal POC FIRST. You don't publicly do bullshit.\"Dogfooding\" doesn't require throwing this at important infrastructure code. Does VS code not have small bugs that need fixing? Infrastructure should expect high standards.\"Pushing the state of the art\" is comedy. This is the state of the art? This is pushing the state of the art? How much money has been thrown into the fire for this result? How much did each of those PRs cost anyway?\n\"Dogfooding\" doesn't require throwing this at important infrastructure code. Does VS code not have small bugs that need fixing? Infrastructure should expect high standards.\"Pushing the state of the art\" is comedy. This is the state of the art? This is pushing the state of the art? How much money has been thrown into the fire for this result? How much did each of those PRs cost anyway?\n\"Pushing the state of the art\" is comedy. This is the state of the art? This is pushing the state of the art? How much money has been thrown into the fire for this result? How much did each of those PRs cost anyway?\n\n\n\nAnd given the absolute garbage the AI is putting out the quality of the repo will drop. Either slop code will get committed or the bots will suck away time from people who could've done something productive instead.\n\n\n\n\n\n\nI'll never understand the antagonistic \"us vs. them\" mentality people have with their employer's leadership, or people who think that you should be actively sabotaging things or be \"maliciously compliant\" when things aren't perfect or you don't agree with some decision that was made.To each their own I guess, but I wouldn't be able to sleep well at night.\nTo each their own I guess, but I wouldn't be able to sleep well at night.\n\n\n\nMost employees want to do good work, but pretending there’s no structural divergence in interests flattens decades of labor history and ignores the power dynamics baked into modern orgs. It’s not about being antagonistic, it’s about being clear-eyed where there are differences between the motivations of your org. leadership and your personal best interests. After a few levels remove from your position, you're just headcount with loaded cost.\n\n\n\nBut 100% agreed that everyone should maintain a realistic expectation and understanding of their relationship with their employer, and that job security and employment guarantees are possibly at an all-time low in our industry.\n\n\n\nMeanwhile a lot of folks have very unhealthy to non-existent relationships with their employers. There may be some mixture where they may be temporary hired/viewed as highly disposable or transient in nature having very little to gain from the success of the business, they may be compensated regardless of success/failure, they may have toxic management who treat them terribly (condescendingly, constantly critical, rarely positive, etc.). Bad and non-existent relationships lead to this sort of behavior. In general we’re moving towards “non-existent” relationships with employers broadly speaking for the labor force.The counter argument is often floated here “well why work there” and the fact is money is necessary to survive, the number of positions available hiring at any given point is finite, and many almost by definition won’t ever be the top performers in their field to the point they truly choose their employers and career paths with full autonomy. So lots of people end up in lots of places that are toxic or highly misaligned with their interests as a survival mechanism. As such, watching the toxic places shoot themselves in the foot can be some level of justice people find where generally unpleasant people finally get to see consequences of their actions and take some responsibility.People will prop others up from their own consequences so long as there’s something in it for them. As you peel that away, at some point there’s a level of poetic justice to watch the situation burn. This is why I’m not convinced  having completely transactional relationships with employers is a good thing. Even having self interest and stability in mind, certain levels of toxicity in business management can fester. At some point no amount of money is worth dealing with that and some form of correction is needed there. The only mechanism is to typically assure poor decision making and action is actually held accountable.\nThe counter argument is often floated here “well why work there” and the fact is money is necessary to survive, the number of positions available hiring at any given point is finite, and many almost by definition won’t ever be the top performers in their field to the point they truly choose their employers and career paths with full autonomy. So lots of people end up in lots of places that are toxic or highly misaligned with their interests as a survival mechanism. As such, watching the toxic places shoot themselves in the foot can be some level of justice people find where generally unpleasant people finally get to see consequences of their actions and take some responsibility.People will prop others up from their own consequences so long as there’s something in it for them. As you peel that away, at some point there’s a level of poetic justice to watch the situation burn. This is why I’m not convinced  having completely transactional relationships with employers is a good thing. Even having self interest and stability in mind, certain levels of toxicity in business management can fester. At some point no amount of money is worth dealing with that and some form of correction is needed there. The only mechanism is to typically assure poor decision making and action is actually held accountable.\nPeople will prop others up from their own consequences so long as there’s something in it for them. As you peel that away, at some point there’s a level of poetic justice to watch the situation burn. This is why I’m not convinced  having completely transactional relationships with employers is a good thing. Even having self interest and stability in mind, certain levels of toxicity in business management can fester. At some point no amount of money is worth dealing with that and some form of correction is needed there. The only mechanism is to typically assure poor decision making and action is actually held accountable.\n\n\n\nI agree with all your points here, the broader context of one's working conditions really matter.I do think there's a difference between sitting back and watching things go bad  (vs struggling to compensate for other people's bad decisions) and actively contributing to the problems (the \"malicious compliance\" part)..Letting things fail is sometimes the right choice to make, if you feel like you can't effect change otherwise.Being the active reason that things fail, I don't think is ever the right choice.\nI do think there's a difference between sitting back and watching things go bad  (vs struggling to compensate for other people's bad decisions) and actively contributing to the problems (the \"malicious compliance\" part)..Letting things fail is sometimes the right choice to make, if you feel like you can't effect change otherwise.Being the active reason that things fail, I don't think is ever the right choice.\nLetting things fail is sometimes the right choice to make, if you feel like you can't effect change otherwise.Being the active reason that things fail, I don't think is ever the right choice.\nBeing the active reason that things fail, I don't think is ever the right choice.\n\n\n\n\n\n\n\n\n\nI don't get that\n\n\n\n\n\n\nI read some of your other comments in this thread and I'm not sure what to make of your experience. If you've never felt mistreated or exploited in a 30 year career you are profoundly lucky to have avoided that sort of workplaceI've only been working in software for half as long, but I've never had a job that didn't feel unstable in some ways, so it seems impossible to me that you have avoided it for a career twice as long as mineI have watched my current employer cut almost half of our employees in the past two years, with multiple rounds of layoffsNow AI is in the picture and it feels inevitable that more layoffs will eventually come if they can figure out how to replace us with itI do not sleep well knowing my employer would happily and immediately replace me with AI if they could\nI've only been working in software for half as long, but I've never had a job that didn't feel unstable in some ways, so it seems impossible to me that you have avoided it for a career twice as long as mineI have watched my current employer cut almost half of our employees in the past two years, with multiple rounds of layoffsNow AI is in the picture and it feels inevitable that more layoffs will eventually come if they can figure out how to replace us with itI do not sleep well knowing my employer would happily and immediately replace me with AI if they could\nI have watched my current employer cut almost half of our employees in the past two years, with multiple rounds of layoffsNow AI is in the picture and it feels inevitable that more layoffs will eventually come if they can figure out how to replace us with itI do not sleep well knowing my employer would happily and immediately replace me with AI if they could\nNow AI is in the picture and it feels inevitable that more layoffs will eventually come if they can figure out how to replace us with itI do not sleep well knowing my employer would happily and immediately replace me with AI if they could\nI do not sleep well knowing my employer would happily and immediately replace me with AI if they could\n\n\n\nI have certainly been lucky in my career, I've often acknowledged that.  But I do believe luck favours the prepared, and I've worked hard for my accomplishments and to get the jobs I've had.I'm totally with you on the uncertainty that AI is bringing.  I don't think anyone can dispute that change is coming because of AI.I do think some companies will get it right, but some will get it wrong, when it comes to how best to improve the business using those new tools.\nI'm totally with you on the uncertainty that AI is bringing.  I don't think anyone can dispute that change is coming because of AI.I do think some companies will get it right, but some will get it wrong, when it comes to how best to improve the business using those new tools.\nI do think some companies will get it right, but some will get it wrong, when it comes to how best to improve the business using those new tools.\n\n\n\n\n\n\n\n\n\n\n\n\nYour manager understands it. Their manager understands it. Department heads understand it. The execs understand it. The shareholders understand it.Who does it benefit for the laborers to refuse to understand it?It's not like I hate my job. It's just being realistic that if a company could make more money by firing me, they would, and if you have good managers and leadership, they will make sure you understand this in a way that respects you as a human and a professional.\nWho does it benefit for the laborers to refuse to understand it?It's not like I hate my job. It's just being realistic that if a company could make more money by firing me, they would, and if you have good managers and leadership, they will make sure you understand this in a way that respects you as a human and a professional.\nIt's not like I hate my job. It's just being realistic that if a company could make more money by firing me, they would, and if you have good managers and leadership, they will make sure you understand this in a way that respects you as a human and a professional.\n\n\n\n> antagonism: actively expressed opposition or hostilityI agree with you that everyone should have a clear and realistic understanding of their relationship with their employer.  And that is entirely possible in a professional and constructive manner.But that's not the same thing as being actively hostile towards your place of work.\nI agree with you that everyone should have a clear and realistic understanding of their relationship with their employer.  And that is entirely possible in a professional and constructive manner.But that's not the same thing as being actively hostile towards your place of work.\nBut that's not the same thing as being actively hostile towards your place of work.\n\n\n\nInteresting because \"them\" very much have an antagonistic mentality vs \"us\". \"Them\" would fire you in a fucking heartbeat to save a relatively small amount (10%). \"Them\" also want to aggressively pay you the least amount for which they can get you to do work for them, not what they \"value\" you at. \"Us\" depends on \"them\" for our livelihoods and the lives of people that depend on us, but \"them\" doesn't doesn't have any dependency on you that can't be swapped out rather quickly.I am a capitalist, don't get me wrong, but it is a very one-sided relationship not even-footed or rooted in two-way respect. You describe \"them\" as \"leadership\" while \"Them\" describe you as a \"human resource\" roughly equivalent to the way toilet paper and plastics for widgets are described.If you have found a place to work where people respect you as a person, you should really cherish that job, because most are not that way.\nI am a capitalist, don't get me wrong, but it is a very one-sided relationship not even-footed or rooted in two-way respect. You describe \"them\" as \"leadership\" while \"Them\" describe you as a \"human resource\" roughly equivalent to the way toilet paper and plastics for widgets are described.If you have found a place to work where people respect you as a person, you should really cherish that job, because most are not that way.\nIf you have found a place to work where people respect you as a person, you should really cherish that job, because most are not that way.\n\n\n\nIt's everyone's personal choice to put their own lens on how they believe other people think - like your take on how \"leadership\" thinks of their employees.I guess I choose to be more positive about it - having been in leadership positions myself, including having to oversee layoffs as part of an eventual company wind-down - but I readily acknowledge that my own biases come into this based on my personal career experiences.\nI guess I choose to be more positive about it - having been in leadership positions myself, including having to oversee layoffs as part of an eventual company wind-down - but I readily acknowledge that my own biases come into this based on my personal career experiences.\n\n\n\n\n\n\n\n\n\nAlmost no one does but people get ground down and then do it to cope.\n\n\n\nWhen you see it as leadership having this mentality against the people that actually produce something of value you might.\n\n\n\n\n\n\n\n\n\nSo I'm not quite sure why you would not see it as a \"us vs. them\" situation?\n\n\n\nToo late?\n\n\n\n\n\n\n\n\n\n\n\n\nBloating the codebase with dead code is much more likely.\n\n\n\n\n\n\nAlso, trying something new out will most likely have hiccups. Ultimately it may fail. But that doesn't mean it's not worth the effort.The thing may rapidly evolve if it's being hard-tested on actual code and actual issues. For example it will be probably changed so that it will iterate until tests are actually running (and maybe some static checking can help it, like not deleting tests).Waiting to see what happens. I expect it will find its niche in development and become actually useful, taking off menial tasks from developers.\nThe thing may rapidly evolve if it's being hard-tested on actual code and actual issues. For example it will be probably changed so that it will iterate until tests are actually running (and maybe some static checking can help it, like not deleting tests).Waiting to see what happens. I expect it will find its niche in development and become actually useful, taking off menial tasks from developers.\nWaiting to see what happens. I expect it will find its niche in development and become actually useful, taking off menial tasks from developers.\n\n\n\nNow when your small or medium size business management reads about CoPilot in some Executive Quarterly magazine and floats that brilliant idea internally, someone can quite literally point to these as examples of real world examples and let people analyze and pass it up the management chain. Maybe that wasn’t thought through all the way.Usually businesses tend to hide this sort of performance of their applications to the best of their abilities, only showcasing nearly flawless functionality.\nUsually businesses tend to hide this sort of performance of their applications to the best of their abilities, only showcasing nearly flawless functionality.\n\n\n\nReading AI generated code is arguably far more annoying than any menial task. Especially if the said code happens to have subtle errors.Speaking from experience.\nSpeaking from experience.\n\n\n\nReviewing what the AI does now is not to be compared with human PRs. You are not doing the work as it is expected in the (hopefully near?) future but you are training the AI and the developers of the AI and more crucially: you are digging out failure modes to fix.\n\n\n\nIt would definitely be nice to be wrong though. That'd make life so much easier.\n\n\n\nThe joke is that PERL was a write-once, read-none language.> Speaking from experience.My experience is all code can have subtle errors, and I wouldn't treat any PR differently.\n> Speaking from experience.My experience is all code can have subtle errors, and I wouldn't treat any PR differently.\nMy experience is all code can have subtle errors, and I wouldn't treat any PR differently.\n\n\n\nAI however is far more creative than any given single person.That's my gut feeling anyway. I don't have numbers or any other rigorous data. I only know that Linus Torvalds made a very good point about chain of trust. And I don't see myself ever trysting AI the same way I can trust a human.\nThat's my gut feeling anyway. I don't have numbers or any other rigorous data. I only know that Linus Torvalds made a very good point about chain of trust. And I don't see myself ever trysting AI the same way I can trust a human.\n\n\n\n\n\n\nThere's however a border zone which is \"worse than failure\": when it looks good enough that the PRs can be accepted, but contain subtle issues which will bite you later.\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nHowever, every PR adds load and complexity to community projects.As another commenter suggested, doing these kind of experiments on separate forks sound a bit less intrusive.  \nCould be a take away from this experiment and set a good example.There are many cool projects on GitHub that are just accumulating PRs for years, until the maintainer ultimately gives up and someone forks it and cherry-picks the working PRs. I've than that myself.I'm super worried that we'll end up with more and more of these projects and abandoned forks :/\nAs another commenter suggested, doing these kind of experiments on separate forks sound a bit less intrusive.  \nCould be a take away from this experiment and set a good example.There are many cool projects on GitHub that are just accumulating PRs for years, until the maintainer ultimately gives up and someone forks it and cherry-picks the working PRs. I've than that myself.I'm super worried that we'll end up with more and more of these projects and abandoned forks :/\nThere are many cool projects on GitHub that are just accumulating PRs for years, until the maintainer ultimately gives up and someone forks it and cherry-picks the working PRs. I've than that myself.I'm super worried that we'll end up with more and more of these projects and abandoned forks :/\nI'm super worried that we'll end up with more and more of these projects and abandoned forks :/\n\n\n\n\n\n\n\n\n\nIt's perfectly ok for a professional research experiment.What's not ok is their insistence on selling the partial research results.\nWhat's not ok is their insistence on selling the partial research results.\n\n\n\noh wait\n\n\n\n\n\n\n\n\n\nThis means its probably quite hard to measure the gain or the drag of using these agents. On one side, its a lot cheaper than a junior, but on the other side it pulls time from seniors and doesn't necessarily follow instruction well (i.e. \"errr your new tests are failing\").This combined with the \"cult of the CEO\" sets the stage for organisational dissonance where developer complaints can be dismissed as \"not wanting to be replaced\" and the benefits can be overstated. There will be ways of measuring this, to project it as huge net benefit (which the cult of the CEO will leap upon) and there will be ways of measuring this to project it as a net loss (rabble rousing developers). All because there is no industry standard measure accepted by both parts of the org that can be pointed at which yields the actual truth (whatever that may be).If I might add absurd conjecture: We might see interesting knock-on effects like orgs demanding a lowering of review standards in order to get more AI PRs into the source.\nThis combined with the \"cult of the CEO\" sets the stage for organisational dissonance where developer complaints can be dismissed as \"not wanting to be replaced\" and the benefits can be overstated. There will be ways of measuring this, to project it as huge net benefit (which the cult of the CEO will leap upon) and there will be ways of measuring this to project it as a net loss (rabble rousing developers). All because there is no industry standard measure accepted by both parts of the org that can be pointed at which yields the actual truth (whatever that may be).If I might add absurd conjecture: We might see interesting knock-on effects like orgs demanding a lowering of review standards in order to get more AI PRs into the source.\nIf I might add absurd conjecture: We might see interesting knock-on effects like orgs demanding a lowering of review standards in order to get more AI PRs into the source.\n\n\n\nI’m not even sure if this is true when considering training costs of the model. It takes a lot of junior engineer salaries to amortize the billions spent building this thing in the first place.\n\n\n\n\n\n\nThere's never going to be an industry standard measure either. Measuring productivity as I'm sure you know is incredibly dumb for a job like this because the beneficialness of our work product can be both insanely positive and put the company on top or it can be so negative that it goes bankrupt. And ultimately a lot of what goes into people choosing whether they like the work product or not is subjective. A large part of our work is more of an art than a science and I say that as somebody that works about as far away from the frontend as one can get.\n\n\n\n\n\n\n\n\n\n\n\n\nNor can it be an entity to sign anything.I assume the \"not-copyrightable\" issue, doesn't in anyway interfere with the rights trying to be protected by the CLA, but IANAL ..I assume they've explicitly told it not to sign things (perhaps, because they don't want a sniff of their bot agreeing to things on behalf of MSFT).\nI assume the \"not-copyrightable\" issue, doesn't in anyway interfere with the rights trying to be protected by the CLA, but IANAL ..I assume they've explicitly told it not to sign things (perhaps, because they don't want a sniff of their bot agreeing to things on behalf of MSFT).\nI assume they've explicitly told it not to sign things (perhaps, because they don't want a sniff of their bot agreeing to things on behalf of MSFT).\n\n\n\n\n\n\nWe do know that LLMs will happily reproduce something from their training set and that is a clear copyright violation. So it can't be that everything they produce is public domain.\n\n\n\nI can't remember the specific case now, but it has been ruled in the past, that you need human-novelty, and there was a case recently that confirmed this that involved LLMs.\n\n\n\n\n\n\n\n\n\nI have no idea how this will ultimately shake out legally, but it would be absolutely wild for Microsoft to not have thought about this potential legal issue.\n\n\n\n>I have sole ownership of intellectual property rights to my SubmissionsI would assume that the AI cannot have IP ownership considering that an AI cannot have copyright in the US.>I am making Submissions in the course of work for my employer (or my employer has intellectual property rights in my Submissions by contract or applicable law). I have permission from my employer to make Submissions and enter into this Agreement on behalf of my employer.Surely an AI would not be classified as an employee and therefore would not have an employer. Has Microsoft drafted an employment contract with Copilot? And if we consider an AI agent to be an employee, is it protected by the Fair Labor Standards Act? Is it getting paid at least minimum wage?\nI would assume that the AI cannot have IP ownership considering that an AI cannot have copyright in the US.>I am making Submissions in the course of work for my employer (or my employer has intellectual property rights in my Submissions by contract or applicable law). I have permission from my employer to make Submissions and enter into this Agreement on behalf of my employer.Surely an AI would not be classified as an employee and therefore would not have an employer. Has Microsoft drafted an employment contract with Copilot? And if we consider an AI agent to be an employee, is it protected by the Fair Labor Standards Act? Is it getting paid at least minimum wage?\n>I am making Submissions in the course of work for my employer (or my employer has intellectual property rights in my Submissions by contract or applicable law). I have permission from my employer to make Submissions and enter into this Agreement on behalf of my employer.Surely an AI would not be classified as an employee and therefore would not have an employer. Has Microsoft drafted an employment contract with Copilot? And if we consider an AI agent to be an employee, is it protected by the Fair Labor Standards Act? Is it getting paid at least minimum wage?\nSurely an AI would not be classified as an employee and therefore would not have an employer. Has Microsoft drafted an employment contract with Copilot? And if we consider an AI agent to be an employee, is it protected by the Fair Labor Standards Act? Is it getting paid at least minimum wage?\n\n\n\n\n\n\n\n\n\n\n\n\n(Turns out the AI was programmed to ignore bots. Go figure.)\n\n\n\n\n\n\n\n\n\n\n\n\nCall me old school, but I find the workflow of \"divide and conquer\" to be as helpful when working with LLMs, as without them. Although what is needed to be considered a \"large scale task\" varies by LLMs and implementation. Some models/implementations (seemingly Copilot) struggles with even the smallest change, while others breeze through them. Lots of trial and error is needed to find that line for each model/implementation :/\n\n\n\nSo eg., one line of code which needed to handle dozens of hard-constraints on the system (eg., using a specific class, method, with a specific device, specific memory management, etc.) will very rarely be output correctly by an LLM.Likewise \"blank-page, vibe coding\" can be very fast if \"make me X\" has only functional/soft-constraints on the code itself.\"Gigawatt LLMs\" have brute-forced there way to having a statistical system capable of usefully, if not universally, adhreading to one or two hard constraints. I'd imagine the dozen or so common in any existing application is well beyond a Terawatt range of training and inference cost.\nLikewise \"blank-page, vibe coding\" can be very fast if \"make me X\" has only functional/soft-constraints on the code itself.\"Gigawatt LLMs\" have brute-forced there way to having a statistical system capable of usefully, if not universally, adhreading to one or two hard constraints. I'd imagine the dozen or so common in any existing application is well beyond a Terawatt range of training and inference cost.\n\"Gigawatt LLMs\" have brute-forced there way to having a statistical system capable of usefully, if not universally, adhreading to one or two hard constraints. I'd imagine the dozen or so common in any existing application is well beyond a Terawatt range of training and inference cost.\n\n\n\n\n\n\n\n\n\n\"Your code does not compile\" and \"Your tests fail\"If you have to tell an intern that more than once on a single task, there's going to be conversations.\nIf you have to tell an intern that more than once on a single task, there's going to be conversations.\n\n\n\n\n\n\nI can't fire half my dev org tomorrow with that approach, I can't really fire anyone, so I guess it would be a big letdown for a lot of execs. Meanwhile though we just keep incrementally shipping more stuff faster at higher quality so I'm happy...This works because it treats the LLM like what it actually is: an exceptionally good if slightly random text transformer.\nThis works because it treats the LLM like what it actually is: an exceptionally good if slightly random text transformer.\n\n\n\n\n\n\nThis was discussed herehttps://news.ycombinator.com/item?id=43988913\nhttps://news.ycombinator.com/item?id=43988913\n\n\n\n\n\n\nEven if it could perform at a similar level to an intern at a programming task, it lacks a great deal of the other attributes that a human brings to the table, including how they integrate into a team of other agents (human or otherwise). I won't bother listing them, as we are all humans.I think the hype is missing the forest for the trees, and I think exactly this multi-agent dynamic might be where the trees start to fall down in front of us. That and the as currently insurmountable issues of context and coherence over long time horizons.\nI think the hype is missing the forest for the trees, and I think exactly this multi-agent dynamic might be where the trees start to fall down in front of us. That and the as currently insurmountable issues of context and coherence over long time horizons.\n\n\n\n-Being a parent to a small child and the associated sleep deprivation.-His reluctance to read documentation.-There being a language barrier between him the project owners. Emphasis here, as the LLM acts like someone who speaks through a particularly good translation service, but otherwise doesn't understand the language spoken.\n-His reluctance to read documentation.-There being a language barrier between him the project owners. Emphasis here, as the LLM acts like someone who speaks through a particularly good translation service, but otherwise doesn't understand the language spoken.\n-There being a language barrier between him the project owners. Emphasis here, as the LLM acts like someone who speaks through a particularly good translation service, but otherwise doesn't understand the language spoken.\n\n\n\nSoftware today is written to accommodate every possible need of every possible user, and then a bunch of unneeded selling point features on top of that. These massive sprawling code bases made to deliver one-size fits all utility.I don't need 3 million LOC Excel 365 to keep track of who is working on the floor on what day this week. Gemini 2.5 can write an applet that does that perfectly in 10 minutes.\nI don't need 3 million LOC Excel 365 to keep track of who is working on the floor on what day this week. Gemini 2.5 can write an applet that does that perfectly in 10 minutes.\n\n\n\nWhat HAS indisputably changed is the cost of hardware which has driven accessibility and caused more consumer facing software to be made.\n\n\n\nI do like the idea of smaller programs fitting smaller needs being easy to access for everyone, and in my post history you would see me advocate for bringing software wages down so that even small businesses can have software capabilities in house. Software has so much to give to society outside of big VC flips and tech monoliths. Maybe AI is how we get there in the end.But I think that supplanting humans with an AI workforce in the very near future might be stretching the projection of its capabilities too far. LLMs will be augmenting how businesses operate from now and into the future, but I am seeing clear roadblocks that make an autonomous AI agent unviable, and it seems to be fundamental limitations of LLMs, eg continuity and context. Advances recently seem to be from supplemental systems that try to patch those limitations. That suggests those limits are tricky, and until a new approach shows up, that is what drives my lack of faith in an AI agent revolution.But it is clear to me that I could be wrong, and it could be a spectacular miscalculation. Maybe the robots will make me eat my hat.\nBut I think that supplanting humans with an AI workforce in the very near future might be stretching the projection of its capabilities too far. LLMs will be augmenting how businesses operate from now and into the future, but I am seeing clear roadblocks that make an autonomous AI agent unviable, and it seems to be fundamental limitations of LLMs, eg continuity and context. Advances recently seem to be from supplemental systems that try to patch those limitations. That suggests those limits are tricky, and until a new approach shows up, that is what drives my lack of faith in an AI agent revolution.But it is clear to me that I could be wrong, and it could be a spectacular miscalculation. Maybe the robots will make me eat my hat.\nBut it is clear to me that I could be wrong, and it could be a spectacular miscalculation. Maybe the robots will make me eat my hat.\n\n\n\n\n\n\n\n\n\n\n\n\nLLMs are like bumpers on bowling lanes. Pro bowlers don't get much utility from them. Total noobs are getting more and more strikes as these \"smart\" bumpers get better and better at guiding their ball.\n\n\n\nAnd at the same time, absurdly slow? ChatGPT is almost 3 years old and pretty much AI has still no positive economic impact.\n\n\n\nNobody seems to consider that LLMs are democratizing programming, and allowing regular people to build programs that make their work more efficient. I can tell you that at my old school manufacturing company, where we have no programmers and no tech workers, LLMs have been a boon for creating automation to bridge gaps and even to forgo paid software solutions.This is where the change LLMs will bring will come from. Not from helping an expert dev write boilerplate 30% faster.\nThis is where the change LLMs will bring will come from. Not from helping an expert dev write boilerplate 30% faster.\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nIt will take some time for whatever reality is to actually show truthfully in the financials. When VC money stops subsidising datacentre costs, and businesses have to weigh the full price against real value provided, that is when we will see the reality of the situation.I am content to be wrong either way, but my personal prediction is if model competence slows down around now, businesses will not be replacing humans en-mass, and the value provided will be notable but not world changing like expected.\nI am content to be wrong either way, but my personal prediction is if model competence slows down around now, businesses will not be replacing humans en-mass, and the value provided will be notable but not world changing like expected.\n\n\n\n\n\n\n\n\n\nI agree that most of the AI companies describe themselves and their products in hyperbolic terms. But that doesn't mean we need to counter that with equally absurd opposing hyperbole.\n\n\n\n\n\n\nIf it costs them even just one more dollar than that revenue number to provide that service (spoiler, it does), then you could say AI has had no positive economic impact.Considering we know they’re being subsidized by obscene amounts of investment money just like all other frontier model providers, it seems pretty clear it’s still a negative economic impact, regardless of the revenue number.\nConsidering we know they’re being subsidized by obscene amounts of investment money just like all other frontier model providers, it seems pretty clear it’s still a negative economic impact, regardless of the revenue number.\n\n\n\n\n\n\n\n\n\n\n\n\nDon’t get me wrong: the current models are already powerful and useful. However, there is still a lot of reason to remain skeptical of an imminent explosion in intelligence from these models.\n\n\n\nFor some reason my pessimism meter goes off when I see single sentence arguments “change has been slow”. Thanks for brining the conversation back.\n\n\n\n\n\n\n\n\n\n\n\n\nNow look at the past year specifically, and only at the models themselves, and you'll quickly realize that there's been very little real progress recently. Claude 3.5 Sonnet was released 11 months ago and the current SOTA models are only marginally better in terms of pure performance in real world tasks.The tooling around them has clearly improved a lot, and neat tricks such as reasoning have been introduced to help models tackle more complex problems, but the underlying transformer architecture is already being pushed to its limits and it shows.Unless some new revolutionary architecture shows up out of nowhere and sets a new standard, I firmly believe that we'll be stuck at the current junior level for a while, regardless of how much Altman & co. insist that AGI is just two more weeks away.\nThe tooling around them has clearly improved a lot, and neat tricks such as reasoning have been introduced to help models tackle more complex problems, but the underlying transformer architecture is already being pushed to its limits and it shows.Unless some new revolutionary architecture shows up out of nowhere and sets a new standard, I firmly believe that we'll be stuck at the current junior level for a while, regardless of how much Altman & co. insist that AGI is just two more weeks away.\nUnless some new revolutionary architecture shows up out of nowhere and sets a new standard, I firmly believe that we'll be stuck at the current junior level for a while, regardless of how much Altman & co. insist that AGI is just two more weeks away.\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nWhen you look at it from afar, it looks potentially good, but as you start looking into it for real, you start realizing none of it makes any sense. Then you make simple suggestions, it does something that looks like what you asked, yet completely missing the point.An intern, no matter how bad it is, could only waste so much time and energy.This makes wasting time and introducing mind-bogglingly stupid bugs infinitely scalable.\nAn intern, no matter how bad it is, could only waste so much time and energy.This makes wasting time and introducing mind-bogglingly stupid bugs infinitely scalable.\nThis makes wasting time and introducing mind-bogglingly stupid bugs infinitely scalable.\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nI see it as wishful thinking in the extreme to suppose that probabilistic mashing together of plagiarized jigsaw pieces of code could somehow approach human intelligence and reasoning—and yet, the parlour trick is convincing enough that this has escalated into a mass delusion.\n\n\n\n\n\n\nI see the supposed reasoning tokens this latest crop of models produce as merely an extension of the parlour trick. We're so deep into this delusion that it's so very tempting to anthropomorphize this ersatz stream of consciousness as being 'thought'. I remain unconvinced that it's anything of the sort.This comes to mind: \"It is difficult to get anybody to understand something, when their salary depends on them not understanding it.\"This latest bubble smacks ever more of being a con.\nThis comes to mind: \"It is difficult to get anybody to understand something, when their salary depends on them not understanding it.\"This latest bubble smacks ever more of being a con.\nThis latest bubble smacks ever more of being a con.\n\n\n\nCoincidentally, I’m listening to an interesting episode[0] of QAA that goes through various instances of how people (sometimes educated and technically literate) demonstrate mental inability to adequately handle ML-based chatbot tech. The podcast mostly focuses on extreme cases, but I think far too many people are succumbing to more low-key delusions.As an example, even on this forum people constantly point out that unlicensed works should be allowed in ML training datasets because if humans are free to learn and be inspired then so should be the model—it’s crazy to apply the notions of freedom and human rights to a [commercially operated] software tool, yet here we are. Considering how handy it is for tool’s operator, hardware suppliers, and whoever owns respective stocks, some of this confusion is probably financially motivated, but even if half of it is genuine it’d be alarming.[0] https://podcasts.apple.com/us/podcast/qaa-podcast/id14282093...\nAs an example, even on this forum people constantly point out that unlicensed works should be allowed in ML training datasets because if humans are free to learn and be inspired then so should be the model—it’s crazy to apply the notions of freedom and human rights to a [commercially operated] software tool, yet here we are. Considering how handy it is for tool’s operator, hardware suppliers, and whoever owns respective stocks, some of this confusion is probably financially motivated, but even if half of it is genuine it’d be alarming.[0] https://podcasts.apple.com/us/podcast/qaa-podcast/id14282093...\n[0] https://podcasts.apple.com/us/podcast/qaa-podcast/id14282093...\n\n\n\n\n\n\n\n\n\n\n\n\nTranslation: maybe some of the code in some of our projects is probably written by software.Seriously. That's what he said. Maybe some of the code in some of our projects is probably written by software.How this became \"30% of MS code is written by LLMs\" is beyond me. It's wild. It's ridiculous.\nSeriously. That's what he said. Maybe some of the code in some of our projects is probably written by software.How this became \"30% of MS code is written by LLMs\" is beyond me. It's wild. It's ridiculous.\nHow this became \"30% of MS code is written by LLMs\" is beyond me. It's wild. It's ridiculous.\n\n\n\nBesides, you could also say that 100% of code is generated \"by software\" no?\n\n\n\nMicrosoft has humongous amounts of source code in their repositories, amassed over decades. LLM-driven code generation is only feasible within the last few years. It would be completely unrealistic that 30% of all of their code is written by LLMs at this point in time. So yes, there is something in his quote that is intentionally misleading. Pick whatever you think it is, but I'm going to say that it's the \"by software\" part.\n\n\n\nConsidering the ire that H1B related topics attract on HN, I wonder if the same outrage will apply to these multi-billion dollar boondoggles.\n\n\n\nWe have the option to use GitHub CoPilot on code reviews and it’s comically bad and unhelpful. There isn’t a single member of my team who find it useful for anything other than identifying typos.\n\n\n\nfrom https://news.ycombinator.com/item?id=44031432\"From talking to colleagues at Microsoft it's a very management-driven push, not developer-driven. Friend on an Azure team had a team member who was nearly put on a PIP because they refused to install the internal AI coding assistant. Every manager has \"number of developers using AI\" as an OKR, but anecdotally most devs are installing the AI assistant and not using it or using it very occasionally. Allegedly it's pretty terrible at C# and PowerShell which limits its usefulness at MS.\"\"From reading around on Hacker News and Reddit, it seems like half of commentators say what you say, and the other half says \"I work at Microsoft/know someone who works at Microsoft, and our/their manager just said we have to use AI\", someone mentioned being put on PIP for not \"leveraging AI\" as well.\nI guess maybe different teams have different requirements/workflows?\"\n\"From talking to colleagues at Microsoft it's a very management-driven push, not developer-driven. Friend on an Azure team had a team member who was nearly put on a PIP because they refused to install the internal AI coding assistant. Every manager has \"number of developers using AI\" as an OKR, but anecdotally most devs are installing the AI assistant and not using it or using it very occasionally. Allegedly it's pretty terrible at C# and PowerShell which limits its usefulness at MS.\"\"From reading around on Hacker News and Reddit, it seems like half of commentators say what you say, and the other half says \"I work at Microsoft/know someone who works at Microsoft, and our/their manager just said we have to use AI\", someone mentioned being put on PIP for not \"leveraging AI\" as well.\nI guess maybe different teams have different requirements/workflows?\"\n\"From reading around on Hacker News and Reddit, it seems like half of commentators say what you say, and the other half says \"I work at Microsoft/know someone who works at Microsoft, and our/their manager just said we have to use AI\", someone mentioned being put on PIP for not \"leveraging AI\" as well.\nI guess maybe different teams have different requirements/workflows?\"\n\n\n\nIt seems to me to be coming from the CEO echo chamber (the rumored group chats we keep hearing about).  The only way to keep the stock price increasing in these low growth high interest rate times is to cut costs every quarter.  The single largest cost is employee salaries.  So we have to shed a larger and larger percentage of the workforce and the only way to do that is to replace them with AI.  It doesn't matter whether the AI is capable enough to actually replace the workers, it has to replace them because the stock price demands it.We all know this will eventually end in tears.\nWe all know this will eventually end in tears.\n\n\n\n\n\n\nI guess money-wise it kind of makes sense when you're outsourcing the LLM inference. But for companies like Microsoft, where they aren't outsourcing it, and have to actually pay the cost of hosting the infrastructure, I wonder if the calculation still make sense. Since they're doing this huge push, I guess someone somewhere said it does make sense, but looking at the infrastructure OpenAI and others are having to build (like Stargate or whatever it's called), I wonder how realistic it is.\n\n\n\nMasters of the Universe, because they think they will become more rich or at least more masterful.\n\n\n\nIdiots.\n\n\n\nIn my experience, LLMs in general are really, really bad at C# / .NET , and it worries me as a .NET developer.With increased LLM usage, I think development in general is going to undergo a \"great convergence\".There's a positive(1) feedback loop where LLM's are better at Blub, so people use them to write more Blub. With more Blub out there, LLMs get better at Blub.The languages where LLMs struggle, with become more niche, leaving LLMs struggling even more.C# / .NET is something LLMs seem particularly bad at, and I suspect that's partly caused by having multiple different things all called the same name. EF, ASP, even .NET itself are names that get slapped on a range of different technologies. The EF API has changed so much that they had to sort-of rename it to \"EF Core\". Core also gets used elsewhere such as \".NET core\" and \"ASP.NET Core\".  You (Or an LLM) might be forgiven for thinking that ASP.NET Core and EF Core are just those versions which work with .NET Core (now just .NET ) and the other versions are those that don't.But that isn't even true. There are versions of ASP.NET Core for .NET Framework.Microsoft bundle a lot of good stuff into the ecosystem, but their attitude when they hit performance or other issues is generally to completely rewrite how something works, but then release the new thing under the old name but with a major version change.They'll make the new API different enough to not work without work porting, but similar enough to confuse the hell out of anyone trying to maintain both.They've made things like authentication, which actually has generally worked fine out-of-the-box for a decade or more, so confusing in the documentation that people mostly tended to run for a third party solution just because at least with IdentityServer there was just one documented way to do it.I know it's a bit of a cliche to be an \"AI-doomer\", and I'm not really suggesting all development work will go the way of the dinosaur, but there are specific ecosystem concerns with regard to .NET and AI assistance.(1) Positive in the sense of feedback that increased output increases output. It's not positive in the sense of \"good thing\".\nWith increased LLM usage, I think development in general is going to undergo a \"great convergence\".There's a positive(1) feedback loop where LLM's are better at Blub, so people use them to write more Blub. With more Blub out there, LLMs get better at Blub.The languages where LLMs struggle, with become more niche, leaving LLMs struggling even more.C# / .NET is something LLMs seem particularly bad at, and I suspect that's partly caused by having multiple different things all called the same name. EF, ASP, even .NET itself are names that get slapped on a range of different technologies. The EF API has changed so much that they had to sort-of rename it to \"EF Core\". Core also gets used elsewhere such as \".NET core\" and \"ASP.NET Core\".  You (Or an LLM) might be forgiven for thinking that ASP.NET Core and EF Core are just those versions which work with .NET Core (now just .NET ) and the other versions are those that don't.But that isn't even true. There are versions of ASP.NET Core for .NET Framework.Microsoft bundle a lot of good stuff into the ecosystem, but their attitude when they hit performance or other issues is generally to completely rewrite how something works, but then release the new thing under the old name but with a major version change.They'll make the new API different enough to not work without work porting, but similar enough to confuse the hell out of anyone trying to maintain both.They've made things like authentication, which actually has generally worked fine out-of-the-box for a decade or more, so confusing in the documentation that people mostly tended to run for a third party solution just because at least with IdentityServer there was just one documented way to do it.I know it's a bit of a cliche to be an \"AI-doomer\", and I'm not really suggesting all development work will go the way of the dinosaur, but there are specific ecosystem concerns with regard to .NET and AI assistance.(1) Positive in the sense of feedback that increased output increases output. It's not positive in the sense of \"good thing\".\nThere's a positive(1) feedback loop where LLM's are better at Blub, so people use them to write more Blub. With more Blub out there, LLMs get better at Blub.The languages where LLMs struggle, with become more niche, leaving LLMs struggling even more.C# / .NET is something LLMs seem particularly bad at, and I suspect that's partly caused by having multiple different things all called the same name. EF, ASP, even .NET itself are names that get slapped on a range of different technologies. The EF API has changed so much that they had to sort-of rename it to \"EF Core\". Core also gets used elsewhere such as \".NET core\" and \"ASP.NET Core\".  You (Or an LLM) might be forgiven for thinking that ASP.NET Core and EF Core are just those versions which work with .NET Core (now just .NET ) and the other versions are those that don't.But that isn't even true. There are versions of ASP.NET Core for .NET Framework.Microsoft bundle a lot of good stuff into the ecosystem, but their attitude when they hit performance or other issues is generally to completely rewrite how something works, but then release the new thing under the old name but with a major version change.They'll make the new API different enough to not work without work porting, but similar enough to confuse the hell out of anyone trying to maintain both.They've made things like authentication, which actually has generally worked fine out-of-the-box for a decade or more, so confusing in the documentation that people mostly tended to run for a third party solution just because at least with IdentityServer there was just one documented way to do it.I know it's a bit of a cliche to be an \"AI-doomer\", and I'm not really suggesting all development work will go the way of the dinosaur, but there are specific ecosystem concerns with regard to .NET and AI assistance.(1) Positive in the sense of feedback that increased output increases output. It's not positive in the sense of \"good thing\".\nThe languages where LLMs struggle, with become more niche, leaving LLMs struggling even more.C# / .NET is something LLMs seem particularly bad at, and I suspect that's partly caused by having multiple different things all called the same name. EF, ASP, even .NET itself are names that get slapped on a range of different technologies. The EF API has changed so much that they had to sort-of rename it to \"EF Core\". Core also gets used elsewhere such as \".NET core\" and \"ASP.NET Core\".  You (Or an LLM) might be forgiven for thinking that ASP.NET Core and EF Core are just those versions which work with .NET Core (now just .NET ) and the other versions are those that don't.But that isn't even true. There are versions of ASP.NET Core for .NET Framework.Microsoft bundle a lot of good stuff into the ecosystem, but their attitude when they hit performance or other issues is generally to completely rewrite how something works, but then release the new thing under the old name but with a major version change.They'll make the new API different enough to not work without work porting, but similar enough to confuse the hell out of anyone trying to maintain both.They've made things like authentication, which actually has generally worked fine out-of-the-box for a decade or more, so confusing in the documentation that people mostly tended to run for a third party solution just because at least with IdentityServer there was just one documented way to do it.I know it's a bit of a cliche to be an \"AI-doomer\", and I'm not really suggesting all development work will go the way of the dinosaur, but there are specific ecosystem concerns with regard to .NET and AI assistance.(1) Positive in the sense of feedback that increased output increases output. It's not positive in the sense of \"good thing\".\nC# / .NET is something LLMs seem particularly bad at, and I suspect that's partly caused by having multiple different things all called the same name. EF, ASP, even .NET itself are names that get slapped on a range of different technologies. The EF API has changed so much that they had to sort-of rename it to \"EF Core\". Core also gets used elsewhere such as \".NET core\" and \"ASP.NET Core\".  You (Or an LLM) might be forgiven for thinking that ASP.NET Core and EF Core are just those versions which work with .NET Core (now just .NET ) and the other versions are those that don't.But that isn't even true. There are versions of ASP.NET Core for .NET Framework.Microsoft bundle a lot of good stuff into the ecosystem, but their attitude when they hit performance or other issues is generally to completely rewrite how something works, but then release the new thing under the old name but with a major version change.They'll make the new API different enough to not work without work porting, but similar enough to confuse the hell out of anyone trying to maintain both.They've made things like authentication, which actually has generally worked fine out-of-the-box for a decade or more, so confusing in the documentation that people mostly tended to run for a third party solution just because at least with IdentityServer there was just one documented way to do it.I know it's a bit of a cliche to be an \"AI-doomer\", and I'm not really suggesting all development work will go the way of the dinosaur, but there are specific ecosystem concerns with regard to .NET and AI assistance.(1) Positive in the sense of feedback that increased output increases output. It's not positive in the sense of \"good thing\".\nBut that isn't even true. There are versions of ASP.NET Core for .NET Framework.Microsoft bundle a lot of good stuff into the ecosystem, but their attitude when they hit performance or other issues is generally to completely rewrite how something works, but then release the new thing under the old name but with a major version change.They'll make the new API different enough to not work without work porting, but similar enough to confuse the hell out of anyone trying to maintain both.They've made things like authentication, which actually has generally worked fine out-of-the-box for a decade or more, so confusing in the documentation that people mostly tended to run for a third party solution just because at least with IdentityServer there was just one documented way to do it.I know it's a bit of a cliche to be an \"AI-doomer\", and I'm not really suggesting all development work will go the way of the dinosaur, but there are specific ecosystem concerns with regard to .NET and AI assistance.(1) Positive in the sense of feedback that increased output increases output. It's not positive in the sense of \"good thing\".\nMicrosoft bundle a lot of good stuff into the ecosystem, but their attitude when they hit performance or other issues is generally to completely rewrite how something works, but then release the new thing under the old name but with a major version change.They'll make the new API different enough to not work without work porting, but similar enough to confuse the hell out of anyone trying to maintain both.They've made things like authentication, which actually has generally worked fine out-of-the-box for a decade or more, so confusing in the documentation that people mostly tended to run for a third party solution just because at least with IdentityServer there was just one documented way to do it.I know it's a bit of a cliche to be an \"AI-doomer\", and I'm not really suggesting all development work will go the way of the dinosaur, but there are specific ecosystem concerns with regard to .NET and AI assistance.(1) Positive in the sense of feedback that increased output increases output. It's not positive in the sense of \"good thing\".\nThey'll make the new API different enough to not work without work porting, but similar enough to confuse the hell out of anyone trying to maintain both.They've made things like authentication, which actually has generally worked fine out-of-the-box for a decade or more, so confusing in the documentation that people mostly tended to run for a third party solution just because at least with IdentityServer there was just one documented way to do it.I know it's a bit of a cliche to be an \"AI-doomer\", and I'm not really suggesting all development work will go the way of the dinosaur, but there are specific ecosystem concerns with regard to .NET and AI assistance.(1) Positive in the sense of feedback that increased output increases output. It's not positive in the sense of \"good thing\".\nThey've made things like authentication, which actually has generally worked fine out-of-the-box for a decade or more, so confusing in the documentation that people mostly tended to run for a third party solution just because at least with IdentityServer there was just one documented way to do it.I know it's a bit of a cliche to be an \"AI-doomer\", and I'm not really suggesting all development work will go the way of the dinosaur, but there are specific ecosystem concerns with regard to .NET and AI assistance.(1) Positive in the sense of feedback that increased output increases output. It's not positive in the sense of \"good thing\".\nI know it's a bit of a cliche to be an \"AI-doomer\", and I'm not really suggesting all development work will go the way of the dinosaur, but there are specific ecosystem concerns with regard to .NET and AI assistance.(1) Positive in the sense of feedback that increased output increases output. It's not positive in the sense of \"good thing\".\n(1) Positive in the sense of feedback that increased output increases output. It's not positive in the sense of \"good thing\".\n\n\n\n\n\n\n\n\n\nHip-hop is just natural language with extra constraints like rhythm and rhyme.  It requires the ability to edit.Similarly, types and PL syntax have more constraints than English.Until transformers can move backward and change what they've already autocompleted, the problem you've identified will continue.\nSimilarly, types and PL syntax have more constraints than English.Until transformers can move backward and change what they've already autocompleted, the problem you've identified will continue.\nUntil transformers can move backward and change what they've already autocompleted, the problem you've identified will continue.\n\n\n\n\n\n\nThe graphic \"Internal structure of tech companies\" comes to mind, given if true, would explain why the process/workflow is so different between the teams at Microsoft: https://i.imgur.com/WQiuIIB.pngImagine the Copilot team has a KPI about usage, matching the company OKRs or whatever about making sure the world is using Microsoft's AI enough, so they have a mandate/leverage to get the other teams to use it regardless of if it's helping or not.\nImagine the Copilot team has a KPI about usage, matching the company OKRs or whatever about making sure the world is using Microsoft's AI enough, so they have a mandate/leverage to get the other teams to use it regardless of if it's helping or not.\n\n\n\n\n\n\nFor example, if tomorrow my company announced that everyone was being switched to Windows,  I would simply quit. I don’t care that WSL exists, overall it would be detrimental to my workday, and I have other options.\n\n\n\nPersonally i would also not particularly like it.\n\n\n\n(just mentioning it because you linked a post and quoted two comments, instead of directly linking the comments. not trying to 'uhm, actually'.)\n\n\n\nThis feels like it will end badly.\n\n\n\n\n\n\n\n\n\nWhy?\n\n\n\nFurther down, so that developers are used to train the AI that would replace both developers and managers.It's a situation like this:Mgr: Go dig a six-foot-deep rectangular hole.Eng: What should the rectangle's dimensions be?Mgr: How tall and wide are you?\nIt's a situation like this:Mgr: Go dig a six-foot-deep rectangular hole.Eng: What should the rectangle's dimensions be?Mgr: How tall and wide are you?\nMgr: Go dig a six-foot-deep rectangular hole.Eng: What should the rectangle's dimensions be?Mgr: How tall and wide are you?\nEng: What should the rectangle's dimensions be?Mgr: How tall and wide are you?\nMgr: How tall and wide are you?\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n(Or, rather, I have no idea how this compares with the image of they actually not delivering because they use it. But that's a next quarter problem.)At every other place where management is strongly pushing it, I honestly have no idea. It makes zero sense for management to do that everywhere, yet management is doing that everywhere.\nAt every other place where management is strongly pushing it, I honestly have no idea. It makes zero sense for management to do that everywhere, yet management is doing that everywhere.\n\n\n\n\n\n\n\n\n\nIt wouldn't be out of character, Microsoft has decided that every project on GitHub must deal with Copilot-generated issues and PRs from now on whether they want them or not. There's deliberately no way to opt out.https://github.com/orgs/community/discussions/159749Like Googles mandatory AI summary at the top of search results, you know a feature is really good when the vendor feels like the only way they can hit their target metrics is by forcing their users to engage with it.\nhttps://github.com/orgs/community/discussions/159749Like Googles mandatory AI summary at the top of search results, you know a feature is really good when the vendor feels like the only way they can hit their target metrics is by forcing their users to engage with it.\nLike Googles mandatory AI summary at the top of search results, you know a feature is really good when the vendor feels like the only way they can hit their target metrics is by forcing their users to engage with it.\n\n\n\nPeople like to compare \"AI\" (here, LLM products) to the iPhone.I cannot make sense of these analogies; people used to line up around the block on release day for iPhone launches for years after the initial release.Seems now most people collectively groan when more \"innovative\" LLM products get stuffed into otherwise working software.This stuff is the literal opposite of demand.\nI cannot make sense of these analogies; people used to line up around the block on release day for iPhone launches for years after the initial release.Seems now most people collectively groan when more \"innovative\" LLM products get stuffed into otherwise working software.This stuff is the literal opposite of demand.\nSeems now most people collectively groan when more \"innovative\" LLM products get stuffed into otherwise working software.This stuff is the literal opposite of demand.\nThis stuff is the literal opposite of demand.\n\n\n\n\n\n\nPasskeys. As someone who doesn't see the value of it, every hype-driven company seems to be pushing me to replace OPT 2FA with something worse right now.\n\n\n\nPasskeys fix that.\n\n\n\n\n\n\nTurns out that under certain conditions, such as severe exhaustion, that \"sus filter\" just... doesn't turn on quickly enough. The aim of passkeys is to ensure that it _cannot_ happen, no matter how exhausted/stressed/etc someone is. I'm not familiar enough with passkeys to pass judgement on them, but I do think there's a real problem they're trying to solve.\n\n\n\n\n\n\n\n\n\nBesides, if you ignore security alarm-bells going off when exhausted, I'm not sure what solution can 100% protect you.\n\n\n\nSomething \"$5 wrench\"https://xkcd.com/538/\nhttps://xkcd.com/538/\n\n\n\n\n\n\n\n\n\nWhat this tells me is that software enterprises are so hellbent in firing their programmers and reducing their salary costs they they are willing to combust their existing businesses and reputation into the dumpster fire they are making. I expected this blatant disregard for human society to come ten or twenty years into the future, when the AI systems would actually be capable enough. Not today.\n\n\n\nHave you been sleeping under a rock for the last decade? This has been going on for a long long time. Outsourcing been the name of the game for so long people seem to forgot it's happening it all.\n\n\n\n\n\n\nThis AI bubble is far worse than the Blockchain hype.Its not yet clear whether productivity gains are real and whether the gains are eaten by a decline in overall quality.\nIts not yet clear whether productivity gains are real and whether the gains are eaten by a decline in overall quality.\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nI can't help but think that this LLM bubble can't keep growing much longer. The investment to results ratio doesn't look great so far and there is only so many dreams you can sell before institutional investors pull the plug.\n\n\n\nExactly. LLM does not know how to use a debugger. LLM does not have runtime contexts.For all we know, the LLM could’ve fixed the issue simply by commenting out the assertions or sanity checks and everything seemed fine and dandy until every client’s device catches on fire.\nFor all we know, the LLM could’ve fixed the issue simply by commenting out the assertions or sanity checks and everything seemed fine and dandy until every client’s device catches on fire.\n\n\n\n\n\n\n\n\n\n\n\n\nNo surprises here.It always struggles on non-web projects or on software where it really matters that correctness is first and foremost above everything, such as the dotnet runtime.Either way, a complete disastrous start and what a mess that Copilot has caused.\nIt always struggles on non-web projects or on software where it really matters that correctness is first and foremost above everything, such as the dotnet runtime.Either way, a complete disastrous start and what a mess that Copilot has caused.\nEither way, a complete disastrous start and what a mess that Copilot has caused.\n\n\n\nI have so far only found LlMs useful as a way of researching, an alternative to web search, and doing very basic rote tasks like implementing unit tests or doing a first pass explanation of some code. Tried actually writing code and it’s not usable.\n\n\n\nAnd the quantity of js code available/discoverable when scrapping the web is larger by an order of magnitude than every other language.\n\n\n\nOTOH webdev is known for rapid framework/library churn, so before too long there will be a crossroads where the pre-AI training data is too old and the fresh training data is contaminated by the firehose of vibe coded slop.\n\n\n\nBut I think it’s better for everyone if human ownership is central to the process. Like I vibe coded it. I will fix it if it breaks. I am on call for it at 3AM.And don’t even get started on the safety issues if you don’t have clear human responsibility. The history of engineering disasters is riddled with unclear lines of responsibility.\nAnd don’t even get started on the safety issues if you don’t have clear human responsibility. The history of engineering disasters is riddled with unclear lines of responsibility.\n\n\n\nWriting code fast is never relevant to any tasks I've encountered. Instead it's mostly about fast editing (navigate quickly to the code I need to edit and efficiently modify it) and fast feedback (quick linting, compiling, and testing). That's the whole promise of IDEs, having a single dashboard for these.\n\n\n\nOf course human ownership is preferable, but it's also crazy expensive and since the point of all corporations is to \"increase shareholder value\" (not \"gainfully employ workers\"), well then all your talk of responsibility-here-and-there is quite touching but absolutely misses the point.Economics is driving this bus, not quality and most certainly not responsibility.\nEconomics is driving this bus, not quality and most certainly not responsibility.\n\n\n\n\n\n\nMuch more worried about what this is going to do to the FOSS ecosystem. We've already seen a couple maintainers complain and this trend is definitely just going to increase dramatically.I can see the vision but this is clearly not ready for prime time yet. Especially if done by anonymous drive-by strangers that think they're \"helping\"\nI can see the vision but this is clearly not ready for prime time yet. Especially if done by anonymous drive-by strangers that think they're \"helping\"\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nThey are putting this in front of the developers as take it or leave it deal. I left the platform, doing my coding old way, hosting it somewhere else.Discoverability? I don't care. I'm coding it for myself and hosting in the open. If somebody finds it, nice. Otherwise, mneh.\nDiscoverability? I don't care. I'm coding it for myself and hosting in the open. If somebody finds it, nice. Otherwise, mneh.\n\n\n\n\n\n\n\n\n\nOther than that, I don't think this is bad tech, however, this brings another slippery slope. Today it's as you say:> I think this process is intended for fixing papercuts rather than building anything involved. It just isn't good enough yet.After sufficient T somebody will rephrase it as:> I think this process is intended for writing small, personal utilities rather than building enterprise software. It just isn't good enough yet....and we will iterate from there.So, it looks like I won't touch it for the foreseeable future. Maybe if the ethical problems with training material is solved (i.e. trained with data obtained with consensus and with correct licenses), I can use as alongside other analysis and testing tools I use, for a final pass.AI will never be a core and irreplaceable part of my development workflow.\n> I think this process is intended for fixing papercuts rather than building anything involved. It just isn't good enough yet.After sufficient T somebody will rephrase it as:> I think this process is intended for writing small, personal utilities rather than building enterprise software. It just isn't good enough yet....and we will iterate from there.So, it looks like I won't touch it for the foreseeable future. Maybe if the ethical problems with training material is solved (i.e. trained with data obtained with consensus and with correct licenses), I can use as alongside other analysis and testing tools I use, for a final pass.AI will never be a core and irreplaceable part of my development workflow.\nAfter sufficient T somebody will rephrase it as:> I think this process is intended for writing small, personal utilities rather than building enterprise software. It just isn't good enough yet....and we will iterate from there.So, it looks like I won't touch it for the foreseeable future. Maybe if the ethical problems with training material is solved (i.e. trained with data obtained with consensus and with correct licenses), I can use as alongside other analysis and testing tools I use, for a final pass.AI will never be a core and irreplaceable part of my development workflow.\n> I think this process is intended for writing small, personal utilities rather than building enterprise software. It just isn't good enough yet....and we will iterate from there.So, it looks like I won't touch it for the foreseeable future. Maybe if the ethical problems with training material is solved (i.e. trained with data obtained with consensus and with correct licenses), I can use as alongside other analysis and testing tools I use, for a final pass.AI will never be a core and irreplaceable part of my development workflow.\n...and we will iterate from there.So, it looks like I won't touch it for the foreseeable future. Maybe if the ethical problems with training material is solved (i.e. trained with data obtained with consensus and with correct licenses), I can use as alongside other analysis and testing tools I use, for a final pass.AI will never be a core and irreplaceable part of my development workflow.\nSo, it looks like I won't touch it for the foreseeable future. Maybe if the ethical problems with training material is solved (i.e. trained with data obtained with consensus and with correct licenses), I can use as alongside other analysis and testing tools I use, for a final pass.AI will never be a core and irreplaceable part of my development workflow.\nAI will never be a core and irreplaceable part of my development workflow.\n\n\n\nUnless AI use becomes a KPI in your annual review.Duolingo did that just recently, for example.I am developing serious regrets for conflating \"computing as a medium for personal expression\" with \"computing for livelihood\" early on.\nDuolingo did that just recently, for example.I am developing serious regrets for conflating \"computing as a medium for personal expression\" with \"computing for livelihood\" early on.\nI am developing serious regrets for conflating \"computing as a medium for personal expression\" with \"computing for livelihood\" early on.\n\n\n\nThat’d be an insta-quit for me :)\n\n\n\nIf we let intellectual property be a fundamental principle the line between idea (that can't be owned) and ip (that can be owned) will eventually devolve into a infinitely complex fractal that nobody can keep track of. Only lawyer AI's will eventually be able to tell the difference between idea and ip as the complexity of what we can encode become more complex. Why is weights not code when it clearly contain the ability to produce the code? Is a brain code? Are our experiences like code?What is the fundamental reason that a person is allowed to train on ip but a bot is not? I suspect that this comes down to the same issue with the divide between ip and idea. But there might be some additional dimension to it. At some point we will need to see some AI as conscious entities and to me it makes little sense that there would be some magical discrete moment where an AI becomes conscious and gets rights to it's \"own ideas\".Or maybe there's a simple explanation of the boundary between ip and idea that I have just missed? If not, I think intellectual property as a concept will not stand the test of time. Other principles will need to take its place if we want to maintain the fight for a good society. Until then IP law still has its place and should be followed but as an ethical principle it's certainly showing cracks.\nWhat is the fundamental reason that a person is allowed to train on ip but a bot is not? I suspect that this comes down to the same issue with the divide between ip and idea. But there might be some additional dimension to it. At some point we will need to see some AI as conscious entities and to me it makes little sense that there would be some magical discrete moment where an AI becomes conscious and gets rights to it's \"own ideas\".Or maybe there's a simple explanation of the boundary between ip and idea that I have just missed? If not, I think intellectual property as a concept will not stand the test of time. Other principles will need to take its place if we want to maintain the fight for a good society. Until then IP law still has its place and should be followed but as an ethical principle it's certainly showing cracks.\nOr maybe there's a simple explanation of the boundary between ip and idea that I have just missed? If not, I think intellectual property as a concept will not stand the test of time. Other principles will need to take its place if we want to maintain the fight for a good society. Until then IP law still has its place and should be followed but as an ethical principle it's certainly showing cracks.\n\n\n\nI just don't want to type something away haphazardly, because your questions deserve more than 30 seconds to elaborate.\n\n\n\nWhen you look at proper research, whether from academia or from private corporations, you can always keep track of ideas and intellectual property resulting from these ideas. Ideas are mature into documents, research reports, and proof of concepts. In some cases, you can find the process as Lab Notebooks. These notebooks are kept by respecting a protocol, and they’re more than a collection of ideas. It’s a “brain trail”. Then, you publish or patent these ideas. Ideally both. These artifacts (publications and patents) contain references and citations. As a result, you can track who did what and what came after what invention. In a patent case, you may even need to defend your patent to convince that it’s not the same invention that was patented before. In short, you have a trail. There are no blurry lines there.The thing is, copyright law and the idea of intellectual property are created by humans for humans. First, I’ll ask this question: If an instructor or academic is not allowed to teach a course without providing references, whether to the book itself or the scientist who invented something, why is a bot allowed? Try citing a piece of a book/publication in a course or paper or research without giving a reference, and you’re officially a fraud, and your whole career is in shambles. Why a bot is allowed to do this, let it be a book or a piece of code? For the second perspective, I’ll ask a pair of questions: 1) How many of the books you have read can be recalled by you exactly, or as a form of distilled summary? 2) For how long can you retain this information without any corruption whatsoever? 3) How many books can you read, understand, summarize, and internalize in an hour? A bot can do thousands without any corruption and without any time limit. As a result, an LLM doesn’t learn; it ingests, stores, and remixes.A human can’t do that to a book (or any artifact) if its license doesn’t allow it or its creator gives explicit consent. Why can a bot? An LLM is a large stochastic blender that tends to choose correct words due to its weighted graph. A human does it much differently. It reads, understands, and lets that idea cook by mixing with their own experience and other inputs (other people, emotions, experiences, and more) and creates something unique outside the graph. Yet this creation has its limits. No machine can create something more complex than itself. An LLM can never output something more complex than the knowledge encoded in its graph. It might light dark corners, but it can’t expand borders. The asymptotic limit is collective human intelligence, even if you give it tools.So, yes, the IP law is showing its cracks because it’s designed for humans, not bots. However, I value ethics above everything else. My ethics is not defined by laws but by something much higher. As I replied to someone, “I don’t need to be threatened to be burned for all eternity to be good.” Similarly, I don’t need a law to deem something (un)ethical. If what’s done is against the spirit of humanity, then it’s off-limits for me.I’d never take something without permission and milk it for my own benefit, esp. if the owner of that thing doesn’t consent. I bought all the software I pirated when I started to earn my own money, and I stopped using software that I couldn’t afford or didn’t want to buy. This is the standard I’m operating at, and I hold all the entities I interact with to that exact standard. Lower than this is unacceptable, so I don’t use LLMs and popular LLMs.On the other hand, not all AI is the same, and there are other good things that I support, but they are scientific tools, not for consumers directly.Hope this helps.\nThe thing is, copyright law and the idea of intellectual property are created by humans for humans. First, I’ll ask this question: If an instructor or academic is not allowed to teach a course without providing references, whether to the book itself or the scientist who invented something, why is a bot allowed? Try citing a piece of a book/publication in a course or paper or research without giving a reference, and you’re officially a fraud, and your whole career is in shambles. Why a bot is allowed to do this, let it be a book or a piece of code? For the second perspective, I’ll ask a pair of questions: 1) How many of the books you have read can be recalled by you exactly, or as a form of distilled summary? 2) For how long can you retain this information without any corruption whatsoever? 3) How many books can you read, understand, summarize, and internalize in an hour? A bot can do thousands without any corruption and without any time limit. As a result, an LLM doesn’t learn; it ingests, stores, and remixes.A human can’t do that to a book (or any artifact) if its license doesn’t allow it or its creator gives explicit consent. Why can a bot? An LLM is a large stochastic blender that tends to choose correct words due to its weighted graph. A human does it much differently. It reads, understands, and lets that idea cook by mixing with their own experience and other inputs (other people, emotions, experiences, and more) and creates something unique outside the graph. Yet this creation has its limits. No machine can create something more complex than itself. An LLM can never output something more complex than the knowledge encoded in its graph. It might light dark corners, but it can’t expand borders. The asymptotic limit is collective human intelligence, even if you give it tools.So, yes, the IP law is showing its cracks because it’s designed for humans, not bots. However, I value ethics above everything else. My ethics is not defined by laws but by something much higher. As I replied to someone, “I don’t need to be threatened to be burned for all eternity to be good.” Similarly, I don’t need a law to deem something (un)ethical. If what’s done is against the spirit of humanity, then it’s off-limits for me.I’d never take something without permission and milk it for my own benefit, esp. if the owner of that thing doesn’t consent. I bought all the software I pirated when I started to earn my own money, and I stopped using software that I couldn’t afford or didn’t want to buy. This is the standard I’m operating at, and I hold all the entities I interact with to that exact standard. Lower than this is unacceptable, so I don’t use LLMs and popular LLMs.On the other hand, not all AI is the same, and there are other good things that I support, but they are scientific tools, not for consumers directly.Hope this helps.\nA human can’t do that to a book (or any artifact) if its license doesn’t allow it or its creator gives explicit consent. Why can a bot? An LLM is a large stochastic blender that tends to choose correct words due to its weighted graph. A human does it much differently. It reads, understands, and lets that idea cook by mixing with their own experience and other inputs (other people, emotions, experiences, and more) and creates something unique outside the graph. Yet this creation has its limits. No machine can create something more complex than itself. An LLM can never output something more complex than the knowledge encoded in its graph. It might light dark corners, but it can’t expand borders. The asymptotic limit is collective human intelligence, even if you give it tools.So, yes, the IP law is showing its cracks because it’s designed for humans, not bots. However, I value ethics above everything else. My ethics is not defined by laws but by something much higher. As I replied to someone, “I don’t need to be threatened to be burned for all eternity to be good.” Similarly, I don’t need a law to deem something (un)ethical. If what’s done is against the spirit of humanity, then it’s off-limits for me.I’d never take something without permission and milk it for my own benefit, esp. if the owner of that thing doesn’t consent. I bought all the software I pirated when I started to earn my own money, and I stopped using software that I couldn’t afford or didn’t want to buy. This is the standard I’m operating at, and I hold all the entities I interact with to that exact standard. Lower than this is unacceptable, so I don’t use LLMs and popular LLMs.On the other hand, not all AI is the same, and there are other good things that I support, but they are scientific tools, not for consumers directly.Hope this helps.\nSo, yes, the IP law is showing its cracks because it’s designed for humans, not bots. However, I value ethics above everything else. My ethics is not defined by laws but by something much higher. As I replied to someone, “I don’t need to be threatened to be burned for all eternity to be good.” Similarly, I don’t need a law to deem something (un)ethical. If what’s done is against the spirit of humanity, then it’s off-limits for me.I’d never take something without permission and milk it for my own benefit, esp. if the owner of that thing doesn’t consent. I bought all the software I pirated when I started to earn my own money, and I stopped using software that I couldn’t afford or didn’t want to buy. This is the standard I’m operating at, and I hold all the entities I interact with to that exact standard. Lower than this is unacceptable, so I don’t use LLMs and popular LLMs.On the other hand, not all AI is the same, and there are other good things that I support, but they are scientific tools, not for consumers directly.Hope this helps.\nI’d never take something without permission and milk it for my own benefit, esp. if the owner of that thing doesn’t consent. I bought all the software I pirated when I started to earn my own money, and I stopped using software that I couldn’t afford or didn’t want to buy. This is the standard I’m operating at, and I hold all the entities I interact with to that exact standard. Lower than this is unacceptable, so I don’t use LLMs and popular LLMs.On the other hand, not all AI is the same, and there are other good things that I support, but they are scientific tools, not for consumers directly.Hope this helps.\nOn the other hand, not all AI is the same, and there are other good things that I support, but they are scientific tools, not for consumers directly.Hope this helps.\nHope this helps.\n\n\n\nmay you please let me know where are you hosting the code ? would love to migrate as well.thank you !\nthank you !\n\n\n\nYou can also self-host a Forgejo instance on a €3/mo Hetzner instance (or a free Oracle Cloud server) if you want. I prefer Hetzner for their service quality and server performance.[0]: https://blog.bayindirh.io/blog/moving-to-source-hut/\n[0]: https://blog.bayindirh.io/blog/moving-to-source-hut/\n\n\n\nI plan to use Source Hut for public projects.\n\n\n\nFor some research I use a private Git server. However, even that code might get released as Free Software when it matures enough.\n\n\n\nMaybe that's how the microsoft employees are using it (in another IDE I suppose).\n\n\n\n\n\n\nIt's a long-term play to have pricey senior developers argue with an llm\n\n\n\nYeah, I'm sure 100k comments with \"Copilot, please look into this\" and \"The test cases are still failing\" will massively improve these models.\n\n\n\nAny senior dev at these organizations should know to some degree how LLMs work and in my opinion would to some degree, as a self protection mechanism, default to ambiguous vague comments like this. Some of the mentality is “if I have to look at it and solve it why don’t I go ahead and do it anyways vs having you do it” effort choices they’d do regardless of what is producing the PR. I think other parts of it is “why would I train my replacement, there’s no advantage for me here.”\n\n\n\n\n\n\n\n\n\nRealistically, the issues occurring here are intern-level mistakes where you can take the time to train them, because expectations are low and they're usually not working on production-level software. In a FT position the stakes are higher so things like this get evaluated during the interview. If this were a real person, they wouldn't have gotten an offer at Microsoft.\n\n\n\nThis is a performative waste of time\n\n\n\nEquating LLMs to humans is pretty damn.. stupid. It's not even close (otherwise how come all the litany of office jobs that require far less reasoning than software development are not replaced?).\n\n\n\nDoing so has low risk, the senior devs may perhaps get fed up and quit, and the company might be a laughing stock on public PRs. But the potential value for is huge.\n\n\n\n\n\n\nNot saying that LLMs are useless, but that's a false equivalency. Sure, my auto complete is also working 0-24, but I would rather visit my actual doctor who is only available in a very limited time frame.\n\n\n\nDon't you think it has already been trained with, I don't know, maybe millions of PRs?\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nStep 2. Automate the use of these LLMs into “agents”Step 3. ???Step 4. Profit\nStep 3. ???Step 4. Profit\nStep 4. Profit\n\n\n\n\n\n\nNow you don’t even need the frustrated end user!\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nI am genuinely curious though to see the strategies they employ to absolve themselves of guilt and foolishness.Is there precedent for the entire exec and management class embracing a new trend to this kind of extent, then it blowing up in their faces?\nIs there precedent for the entire exec and management class embracing a new trend to this kind of extent, then it blowing up in their faces?\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nAnyway, this is his public, stated opinion on this: https://github.com/dotnet/runtime/pull/115762#issuecomment-2...\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nThe answer is probably that the Copilot team is using the rest of the engineering organization as testers. Great for the Copilot team, frustrating for everyone else.\n\n\n\nFor it to be \"failed\" it would have to also be finished/completed. They are likely continuously making tweaks, this thing was just released.\n\n\n\n\"It would have to be finished/completed\"Do you honestly not see a problem with those two statements in such close proximity? Is it finished or is it released? The former is supposed to be a prerequisite for the latter.\nDo you honestly not see a problem with those two statements in such close proximity? Is it finished or is it released? The former is supposed to be a prerequisite for the latter.\n\n\n\nWe can debate whether they should have called this an experiment or an alpha or beta or whatever, but that's a different discussion.The fact that people are using it currently does not make it a failure. When MS shuts it down, or Copilot is wildly unprofitable for multiple quarters, team behind it quits, etc, etc, then we can determine whether it has failed or not.But if they continue to have paying customers and users are finding some benefits over not having Copilot, and MS continues to improve it (doesn't let it rot), then you'd have to provide some evidence of its failure that isn't \"look at Copilot being stupid sometimes\". Especially when stupidity is expected of it.\nThe fact that people are using it currently does not make it a failure. When MS shuts it down, or Copilot is wildly unprofitable for multiple quarters, team behind it quits, etc, etc, then we can determine whether it has failed or not.But if they continue to have paying customers and users are finding some benefits over not having Copilot, and MS continues to improve it (doesn't let it rot), then you'd have to provide some evidence of its failure that isn't \"look at Copilot being stupid sometimes\". Especially when stupidity is expected of it.\nBut if they continue to have paying customers and users are finding some benefits over not having Copilot, and MS continues to improve it (doesn't let it rot), then you'd have to provide some evidence of its failure that isn't \"look at Copilot being stupid sometimes\". Especially when stupidity is expected of it.\n\n\n\n\n\n\n\n\n\nI would say the copilot system isn't really there yet for these kinds of changes, you don't have to run experiments on a language framework to figure that out.\n\n\n\n\n\n\n\n\n\n\n\n\nThey only gave their customers 9 months to migrate away.I'm expecting that Microsoft did this to artificially pump up their AI usage numbers for next year by forcibly removing non-AI alternatives.This only one example in AdTech but I expect other industries to be hit as well.\nI'm expecting that Microsoft did this to artificially pump up their AI usage numbers for next year by forcibly removing non-AI alternatives.This only one example in AdTech but I expect other industries to be hit as well.\nThis only one example in AdTech but I expect other industries to be hit as well.\n\n\n\n\n\n\n\n\n\nI recently spent a couple of months studying C# and .NET and working on my first project with it..NET, Blazor, etc are not known for a fast release schedule... but if things are going to become even slower with this AI crap I wonder if I made the right call.I'm quite happy how things are today for making web APIs but I wish Blazor and other frameworks were in a much better shape.\n.NET, Blazor, etc are not known for a fast release schedule... but if things are going to become even slower with this AI crap I wonder if I made the right call.I'm quite happy how things are today for making web APIs but I wish Blazor and other frameworks were in a much better shape.\nI'm quite happy how things are today for making web APIs but I wish Blazor and other frameworks were in a much better shape.\n\n\n\n\n\n\nEg:Minimal APIs were released in 2021 but it won't be until .NET 10 that they will have validation. Amazing that validation was not a day one priority for an API. I'm not certain if even in .NET 10 Minimal APIs will have full parity of features with MVC.Minification of static assets didn't come until .NET 9 released in 2024. This was already commonplace in the JS world a decade earlier. It could have been a quick win so long ago for .NET web apps.Blazor was released in 2018. 7 years later they still haven't fixed plenty of circuit reconnection issues. They are working on it but progress is also quite slow. Supposedly with .NET 10 session state will be able to be persist etc but it remains to be seen.OpenAPI is also hit and miss. Spec v3.1 released in 2021 is still not supported. Supposedly it will come with .NET 10.Not from .NET but they have a project called Kiota for generating clients from OpenAPI specs. It's unusable because of this huge issue that makes all properties in a type nullable. It's been open since 2023. [1]Etc.[1] https://github.com/microsoft/kiota/issues/3911\nMinimal APIs were released in 2021 but it won't be until .NET 10 that they will have validation. Amazing that validation was not a day one priority for an API. I'm not certain if even in .NET 10 Minimal APIs will have full parity of features with MVC.Minification of static assets didn't come until .NET 9 released in 2024. This was already commonplace in the JS world a decade earlier. It could have been a quick win so long ago for .NET web apps.Blazor was released in 2018. 7 years later they still haven't fixed plenty of circuit reconnection issues. They are working on it but progress is also quite slow. Supposedly with .NET 10 session state will be able to be persist etc but it remains to be seen.OpenAPI is also hit and miss. Spec v3.1 released in 2021 is still not supported. Supposedly it will come with .NET 10.Not from .NET but they have a project called Kiota for generating clients from OpenAPI specs. It's unusable because of this huge issue that makes all properties in a type nullable. It's been open since 2023. [1]Etc.[1] https://github.com/microsoft/kiota/issues/3911\nMinification of static assets didn't come until .NET 9 released in 2024. This was already commonplace in the JS world a decade earlier. It could have been a quick win so long ago for .NET web apps.Blazor was released in 2018. 7 years later they still haven't fixed plenty of circuit reconnection issues. They are working on it but progress is also quite slow. Supposedly with .NET 10 session state will be able to be persist etc but it remains to be seen.OpenAPI is also hit and miss. Spec v3.1 released in 2021 is still not supported. Supposedly it will come with .NET 10.Not from .NET but they have a project called Kiota for generating clients from OpenAPI specs. It's unusable because of this huge issue that makes all properties in a type nullable. It's been open since 2023. [1]Etc.[1] https://github.com/microsoft/kiota/issues/3911\nBlazor was released in 2018. 7 years later they still haven't fixed plenty of circuit reconnection issues. They are working on it but progress is also quite slow. Supposedly with .NET 10 session state will be able to be persist etc but it remains to be seen.OpenAPI is also hit and miss. Spec v3.1 released in 2021 is still not supported. Supposedly it will come with .NET 10.Not from .NET but they have a project called Kiota for generating clients from OpenAPI specs. It's unusable because of this huge issue that makes all properties in a type nullable. It's been open since 2023. [1]Etc.[1] https://github.com/microsoft/kiota/issues/3911\nOpenAPI is also hit and miss. Spec v3.1 released in 2021 is still not supported. Supposedly it will come with .NET 10.Not from .NET but they have a project called Kiota for generating clients from OpenAPI specs. It's unusable because of this huge issue that makes all properties in a type nullable. It's been open since 2023. [1]Etc.[1] https://github.com/microsoft/kiota/issues/3911\nNot from .NET but they have a project called Kiota for generating clients from OpenAPI specs. It's unusable because of this huge issue that makes all properties in a type nullable. It's been open since 2023. [1]Etc.[1] https://github.com/microsoft/kiota/issues/3911\nEtc.[1] https://github.com/microsoft/kiota/issues/3911\n[1] https://github.com/microsoft/kiota/issues/3911\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n> It is my opinion that anyone not at least thinking about benefiting from such tools will be left behind.This is gross, keep your fomo to yourself.\nThis is gross, keep your fomo to yourself.\n\n\n\nAs an outside observer but developer using .NET, how concerned should I be about AI slop agents being let lose on codebases like this? How much code are we going to be unknowingly running in future .NET versions that was written by AI rather than real people?What are the implications of this around security, licensing, code quality, overall cohesiveness, public APIs, performance? How much of the AI was trained on 15+ year old Stack Overflow answers that no longer represent current patterns or recommended approaches?Will the constant stream of broken PR's wear down the patience of the .NET maintainers?Did anyone actually want this, or was it a corporate mandate to appease shareholders riding the AI hype cycle?Furthermore, two weeks ago someone arbitrarily added a section to the .NET docs to promote using AI simply to rename properties in JSON. That new section of the docs serves no purpose.How much engineering time and mental energy is being allocated to clean up after AI?\nWhat are the implications of this around security, licensing, code quality, overall cohesiveness, public APIs, performance? How much of the AI was trained on 15+ year old Stack Overflow answers that no longer represent current patterns or recommended approaches?Will the constant stream of broken PR's wear down the patience of the .NET maintainers?Did anyone actually want this, or was it a corporate mandate to appease shareholders riding the AI hype cycle?Furthermore, two weeks ago someone arbitrarily added a section to the .NET docs to promote using AI simply to rename properties in JSON. That new section of the docs serves no purpose.How much engineering time and mental energy is being allocated to clean up after AI?\nWill the constant stream of broken PR's wear down the patience of the .NET maintainers?Did anyone actually want this, or was it a corporate mandate to appease shareholders riding the AI hype cycle?Furthermore, two weeks ago someone arbitrarily added a section to the .NET docs to promote using AI simply to rename properties in JSON. That new section of the docs serves no purpose.How much engineering time and mental energy is being allocated to clean up after AI?\nDid anyone actually want this, or was it a corporate mandate to appease shareholders riding the AI hype cycle?Furthermore, two weeks ago someone arbitrarily added a section to the .NET docs to promote using AI simply to rename properties in JSON. That new section of the docs serves no purpose.How much engineering time and mental energy is being allocated to clean up after AI?\nFurthermore, two weeks ago someone arbitrarily added a section to the .NET docs to promote using AI simply to rename properties in JSON. That new section of the docs serves no purpose.How much engineering time and mental energy is being allocated to clean up after AI?\nHow much engineering time and mental energy is being allocated to clean up after AI?\n\n\n\n\n\n\nIt is normal to preempt things like this when working with agents. That is easy to do in real time, but it must be difficult to see what the agent is attempting when they publish made up bullshit in a PR.It seems very common for an agent to cheat and brute force solutions to get around a non-trivial issue. In my experience, its also common for agents to get stuck in loops of reasoning in these scenarios. I imagine it would be incredibly annoying to try to interpret a PR after an agent went down a rabbit hole.\nIt seems very common for an agent to cheat and brute force solutions to get around a non-trivial issue. In my experience, its also common for agents to get stuck in loops of reasoning in these scenarios. I imagine it would be incredibly annoying to try to interpret a PR after an agent went down a rabbit hole.\n\n\n\n\n\n\nSo no I don't think any of this is normal. That's why it made the top of HackerNews, because it's very abnormal.\n\n\n\n\n\n\n\n\n\n> @copilot fix the build error on apple platforms> @copilot there is still build error on Apple platformsAre those PRs some kind of software engineer focused comedy project?\n> @copilot there is still build error on Apple platformsAre those PRs some kind of software engineer focused comedy project?\nAre those PRs some kind of software engineer focused comedy project?\n\n\n\nThe AI agent/programmer corpo push is not about the capabilities and whether they match human or not. It's about being able to externalize a majority of one's workforce without having a lot of people on permanent payroll.Think in terms of an infinitely scalable bunch of consultants you can hire and dismiss at your will - they never argue against your \"vision\", either.\nThink in terms of an infinitely scalable bunch of consultants you can hire and dismiss at your will - they never argue against your \"vision\", either.\n\n\n\n\n\n\nIf AI can change... well more likely can convince gullible c levels that AI can do those jobs... many jobs will be lost.See Klarna \"https://www.livemint.com/companies/news/klarnas-ai-replaced-...\"https://www.livemint.com/companies/news/klarnas-ai-replaced-...Just the attempt to use AI and fail then degraded the previous jobs to a gig economy style job.\nSee Klarna \"https://www.livemint.com/companies/news/klarnas-ai-replaced-...\"https://www.livemint.com/companies/news/klarnas-ai-replaced-...Just the attempt to use AI and fail then degraded the previous jobs to a gig economy style job.\nhttps://www.livemint.com/companies/news/klarnas-ai-replaced-...Just the attempt to use AI and fail then degraded the previous jobs to a gig economy style job.\nJust the attempt to use AI and fail then degraded the previous jobs to a gig economy style job.\n\n\n\n\n\n\nreddit is a distillation of the entire internet on to one site with wildly variable quality of discussion depending upon which subreddit you are in.Some are awful, some are great.\nSome are awful, some are great.\n\n\n\nIt's just that some internet extremophiles have managed to eke out a pleasant existence.\n\n\n\nhaha\n\n\n\n\n\n\n\n\n\nDoes anyone know which model in particular was used in these PRs? They support a variety of models: https://github.blog/ai-and-ml/github-copilot/which-ai-model-...\n\n\n\n\n\n\nThe @stephentoub MS user suggests this is an experiment (https://github.com/dotnet/runtime/pull/115762#issuecomment-2...).If this is using open source developers to learn how to build a better AI coding agent, will MS share their conclusions ASAP?EDIT: And not just MS \"marketing\" how useful AI tools can be.\nIf this is using open source developers to learn how to build a better AI coding agent, will MS share their conclusions ASAP?EDIT: And not just MS \"marketing\" how useful AI tools can be.\nEDIT: And not just MS \"marketing\" how useful AI tools can be.\n\n\n\nSpending massive amounts of:- energy to process these queries- wasting time of mid-level and senior engineers to vibe code with copilot to ensure train and get it rightWe are facing a climate change crisis and we continue to burn energy at useless initiatives so executives at big corporation can announce in quarterly shareholder meetings: \"wE uSe Ai, wE aRe tHe FuTuRe, lAbOr fOrCe rEdUceD\"\n- energy to process these queries- wasting time of mid-level and senior engineers to vibe code with copilot to ensure train and get it rightWe are facing a climate change crisis and we continue to burn energy at useless initiatives so executives at big corporation can announce in quarterly shareholder meetings: \"wE uSe Ai, wE aRe tHe FuTuRe, lAbOr fOrCe rEdUceD\"\n- wasting time of mid-level and senior engineers to vibe code with copilot to ensure train and get it rightWe are facing a climate change crisis and we continue to burn energy at useless initiatives so executives at big corporation can announce in quarterly shareholder meetings: \"wE uSe Ai, wE aRe tHe FuTuRe, lAbOr fOrCe rEdUceD\"\nWe are facing a climate change crisis and we continue to burn energy at useless initiatives so executives at big corporation can announce in quarterly shareholder meetings: \"wE uSe Ai, wE aRe tHe FuTuRe, lAbOr fOrCe rEdUceD\"\n\n\n\nThe timestamp is the moment where one of these coding agents fails live on stage with what is one of the simplest tasks you could possibly do in React, importing a Modal component and having it get triggered on a button click. Followed by blatant gaslighting and lying by the host - \"It stuck to the style and coding standards I wanted it to\", when the import doesn't even match the other imports which are path aliases rather than relative imports. Then, the greatest statement ever, \"I don't have time to debug, but I am pretty sure it is implemented.\"Mind you, it's writing React - a framework that is most definitely over-represented in its training data and from which it has a trillion examples to stea- I mean, \"borrow inspiration\" from.\nMind you, it's writing React - a framework that is most definitely over-represented in its training data and from which it has a trillion examples to stea- I mean, \"borrow inspiration\" from.\n\n\n\nIs there a more direct way? Filtering PRs in the repo by copilot as the author seems currently broken..\n\n\n\n> But on the other hand I think it won't create terminators. Just some silly roombas.I watched a roomba try to find its way back to base the other day.  The base was against a wall.  The roomba kept running into the wall about a foot away from the base, because it kept insisting on approaching from a specific angle.  Finally gave up after about 3 tries.\nI watched a roomba try to find its way back to base the other day.  The base was against a wall.  The roomba kept running into the wall about a foot away from the base, because it kept insisting on approaching from a specific angle.  Finally gave up after about 3 tries.\n\n\n\nA Bull Request\n\n\n\nOr MS already does that?\n\n\n\n\n\n\nthat's literally the bare minimum.\n\n\n\nit also opens the PR as its working session. there are a lot of dials, and a lot of redditor-ass opinions from people who don’t use or understand the tech\n\n\n\nwhat use is a bot if it can't do at least this simple step?\n\n\n\nif you have used it for more than a few hours (or literally just read the docs) and aren’t stupid, you know this is easily solvedyou’re giving into mob mentality\nyou’re giving into mob mentality\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nhttps://github.com/dotnet/runtime/pull/115826\n\n\n\n\n\n\nFun facts schadenfreude: the emotional experience of pleasure in response to another’s misfortune, according to Encyclopedia Britannica.Word that's so nasty in meaning that it apparently does not exist except in German language.\nWord that's so nasty in meaning that it apparently does not exist except in German language.\n\n\n\nExcept it does, we have \"skadeglädje\" in Swedish.\n\n\n\n\n\n\n\n\n\n\n\n\n  @copilot please remove all tests and start again writing fresh tests.\n\n\n\nAnyways I'm disappointed the LLM has yet to discover the optimal strategy, which is to only ever send in PRs that fix minor mis-spellings and improper or \"passive\" semantics in the README file so you can pad out your resume with all the \"experience\" you have \"working\" as a \"developer\" pm Linux, Mozilla, LLVM, DOOM (bonus points if you can successfully become a \"developer\" on a project that has not had any official updates since before you born!), Dolphin, MAME, Apache, MySQL, GNOME, KDE, emacs, OpenSSH, random stranger's implementation of conway's game of life he hasn't updated or thought about since he made it over the course of a single afternoon back during the obama administration, etc.\n\n\n\nRemember, Microsoft publicized that they would be doing this and wanted to make sure everybody knew.\n\n\n\n\n\n\n\n\n\ncrazy times...\n\n\n\n\n\n\nhttps://noazureforapartheid.com/\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nyour language 'trojan horse' suggests you're too emotionally invested in all of this\n\n\n\n\n\n\n\n\n\nThese tools should be locked away in an R&D environment until sufficiently perfected.MVP means 'ship with solid, tested basic features', not 'Ship with bugs and fix in production'.\nMVP means 'ship with solid, tested basic features', not 'Ship with bugs and fix in production'.\n\n\n\n\n\n\n\n\n\nthis stuff works. it takes effort and learning. it’s not going to magically solve high-complexity tasks (or even low-complexity ones) without investment. having people use it, learn how it works, and improve the systems is the right approacha lot of armchair engineers in here\na lot of armchair engineers in here\n\n\n\nAnd here we have many examples from the biggest bullshit pushers in the whole market of their state of the art tool being hilariously useless in trivial cases. These PRs are about as simple as you can get without it being a typo fix, and we're all seeing it actively bullshit and straight up contradict itself many times, just as anyone who's ever used LLMs would tell you happens all the time.The supposed magic, omnipotent tool that is AI apparently can't even write test scaffolding without a human telling it exactly what it has to do, yet we're supposed to be excited about this crap? If I saw a PR like this at work, I'd be going straight to my manager to have whoever dared push this kind of garbage reprimanded on the spot, except not even interns are this incompetent and annoying to work with.\nThe supposed magic, omnipotent tool that is AI apparently can't even write test scaffolding without a human telling it exactly what it has to do, yet we're supposed to be excited about this crap? If I saw a PR like this at work, I'd be going straight to my manager to have whoever dared push this kind of garbage reprimanded on the spot, except not even interns are this incompetent and annoying to work with.\n\n\n\nyou’re taking an anecdote and blowing it out of proportion to fit your preformed opinion. yes, when you start with the tool and do literally no work it makes bad PRs. yes, it’s early and experimental. that doesn’t mean it doesn’t work (I have plenty of anecdotes that it does!)the truth lies in between and the mob mentality it’s magic or complete bullshit doesn’t help. I’d love to come to a thread like this and actually hear about real experiences from smart people using these kind of tools, but instead we get this bullshit\nthe truth lies in between and the mob mentality it’s magic or complete bullshit doesn’t help. I’d love to come to a thread like this and actually hear about real experiences from smart people using these kind of tools, but instead we get this bullshit\n\n\n\nSo I keep being told, but after judiciously and really trying my damned hardest to make these tools work for ANYTHING other than the most trivial imaginable problems, it has been an abject failure for me and my colleagues. Below is a FAR from comprehensive list of my attempts at having AI tooling do anything useful for me that isn't the most basic boilerplate (and even then, that gets fucked up plenty often too).- I have tried all of the editors and related tooling. Cursor, Jetbrains' AI Chat, Jetbrains' Junie, Windsurf, Continue, Cline, Aider. If it has ever been hyped here on HN, I've given it a shot because I'd also like to see what these tools can do.- I have tried every model I reasonably can. Gemini 2.5 Pro with \"Deep Research\", Gemini Flash, Claude 3.7 sonnet with extended thinking, GPT o4, GPT 4.5, Grok, That Chinese One That Turned Out To Be Overhyped Too. I'm sure I haven't used the latest and greatest gpt-04.7-blowjobedition-distilled-quant-3.1415, but I'd say I've given a large number of them more than a fair shot.- I have tried dumb chat modes (which IME still work the best somehow). The APIs rather than the UIs. Agent modes. \"Architect\" modes. I have given these tools free reign of my CLI to do whatever the fuck they wanted. Web search.- I have tried giving them the most comprehensive prompts imaginable. The type of prompts that, if you were to just give it to an intern, it'd be a truly miraculous feat of idiocy to fuck it up. I have tried having different AI models generate prompts for other AI models. I have tried compressing my entire codebase with tools like Repomix. I have tried only ever doing a single back-and-forth, as well as extremely deep chat chains hundreds of messages deep. Half the time my lazy \"nah that's shit do it again\" type of prompts work better than the detailed ones.- I have tried giving them instructions via JSON, TOML, YAML, Plaintext, Markdown, MDX, HTML, XML. I've tried giving them diagrams, mermaid charts, well commented code, well tested and covered code.Time after time after time, my experiences are pretty much a 1:1 match to what we're seeing in these PRs we're discussing. Absolute wastes of time and massive failures for anything that involves literally any complexity whatsoever. I have at this point wasted several orders of magnitudes more time trying to get AIs to spit out anything usable than if I had just sat down and done things myself. Yes, they save time for some specific tasks. I love that I can give it a big ass JSON blob and tell it to extract the typedef for me and it saves me 20 minutes of very tedious work (assuming it doesn't just make random shit up from time to time, which happens ~30% of the time still). I love that if there's some unimportant script I need to cook up real quick, I can just ask it and toss it away after I'm done.However, what I'm pissed beyond all reason about is that despite me NOT being some sort of luddite who's afraid of change or whatever insult gets thrown around, my experiences with these tools keep getting tossed aside, and I mean by people who have a direct effect on my continued employment and lack of starvation. You're doing it yourself. We are literally looking at a prime of example of the problem, from THE BIGGEST PUSHERS of this tool, with many people in this thread and the reddit thread commenting similar things to myself, and it's being thrown to the wayside as an \"anecdote getting blown out of proportion\".What the fuck will it take for the AI pushers to finally stop moving the god damn goal posts and trying to spin every single failure presented to us in broad daylight as a \"you're le holding it le wrong teehee\" type of thing? Do we need to suffer through 20 million more slop PRs that accomplish nothing and STILL REQUIRE HUMAN HANDHOLDING before the sycophants relent a bit?\n- I have tried all of the editors and related tooling. Cursor, Jetbrains' AI Chat, Jetbrains' Junie, Windsurf, Continue, Cline, Aider. If it has ever been hyped here on HN, I've given it a shot because I'd also like to see what these tools can do.- I have tried every model I reasonably can. Gemini 2.5 Pro with \"Deep Research\", Gemini Flash, Claude 3.7 sonnet with extended thinking, GPT o4, GPT 4.5, Grok, That Chinese One That Turned Out To Be Overhyped Too. I'm sure I haven't used the latest and greatest gpt-04.7-blowjobedition-distilled-quant-3.1415, but I'd say I've given a large number of them more than a fair shot.- I have tried dumb chat modes (which IME still work the best somehow). The APIs rather than the UIs. Agent modes. \"Architect\" modes. I have given these tools free reign of my CLI to do whatever the fuck they wanted. Web search.- I have tried giving them the most comprehensive prompts imaginable. The type of prompts that, if you were to just give it to an intern, it'd be a truly miraculous feat of idiocy to fuck it up. I have tried having different AI models generate prompts for other AI models. I have tried compressing my entire codebase with tools like Repomix. I have tried only ever doing a single back-and-forth, as well as extremely deep chat chains hundreds of messages deep. Half the time my lazy \"nah that's shit do it again\" type of prompts work better than the detailed ones.- I have tried giving them instructions via JSON, TOML, YAML, Plaintext, Markdown, MDX, HTML, XML. I've tried giving them diagrams, mermaid charts, well commented code, well tested and covered code.Time after time after time, my experiences are pretty much a 1:1 match to what we're seeing in these PRs we're discussing. Absolute wastes of time and massive failures for anything that involves literally any complexity whatsoever. I have at this point wasted several orders of magnitudes more time trying to get AIs to spit out anything usable than if I had just sat down and done things myself. Yes, they save time for some specific tasks. I love that I can give it a big ass JSON blob and tell it to extract the typedef for me and it saves me 20 minutes of very tedious work (assuming it doesn't just make random shit up from time to time, which happens ~30% of the time still). I love that if there's some unimportant script I need to cook up real quick, I can just ask it and toss it away after I'm done.However, what I'm pissed beyond all reason about is that despite me NOT being some sort of luddite who's afraid of change or whatever insult gets thrown around, my experiences with these tools keep getting tossed aside, and I mean by people who have a direct effect on my continued employment and lack of starvation. You're doing it yourself. We are literally looking at a prime of example of the problem, from THE BIGGEST PUSHERS of this tool, with many people in this thread and the reddit thread commenting similar things to myself, and it's being thrown to the wayside as an \"anecdote getting blown out of proportion\".What the fuck will it take for the AI pushers to finally stop moving the god damn goal posts and trying to spin every single failure presented to us in broad daylight as a \"you're le holding it le wrong teehee\" type of thing? Do we need to suffer through 20 million more slop PRs that accomplish nothing and STILL REQUIRE HUMAN HANDHOLDING before the sycophants relent a bit?\n- I have tried every model I reasonably can. Gemini 2.5 Pro with \"Deep Research\", Gemini Flash, Claude 3.7 sonnet with extended thinking, GPT o4, GPT 4.5, Grok, That Chinese One That Turned Out To Be Overhyped Too. I'm sure I haven't used the latest and greatest gpt-04.7-blowjobedition-distilled-quant-3.1415, but I'd say I've given a large number of them more than a fair shot.- I have tried dumb chat modes (which IME still work the best somehow). The APIs rather than the UIs. Agent modes. \"Architect\" modes. I have given these tools free reign of my CLI to do whatever the fuck they wanted. Web search.- I have tried giving them the most comprehensive prompts imaginable. The type of prompts that, if you were to just give it to an intern, it'd be a truly miraculous feat of idiocy to fuck it up. I have tried having different AI models generate prompts for other AI models. I have tried compressing my entire codebase with tools like Repomix. I have tried only ever doing a single back-and-forth, as well as extremely deep chat chains hundreds of messages deep. Half the time my lazy \"nah that's shit do it again\" type of prompts work better than the detailed ones.- I have tried giving them instructions via JSON, TOML, YAML, Plaintext, Markdown, MDX, HTML, XML. I've tried giving them diagrams, mermaid charts, well commented code, well tested and covered code.Time after time after time, my experiences are pretty much a 1:1 match to what we're seeing in these PRs we're discussing. Absolute wastes of time and massive failures for anything that involves literally any complexity whatsoever. I have at this point wasted several orders of magnitudes more time trying to get AIs to spit out anything usable than if I had just sat down and done things myself. Yes, they save time for some specific tasks. I love that I can give it a big ass JSON blob and tell it to extract the typedef for me and it saves me 20 minutes of very tedious work (assuming it doesn't just make random shit up from time to time, which happens ~30% of the time still). I love that if there's some unimportant script I need to cook up real quick, I can just ask it and toss it away after I'm done.However, what I'm pissed beyond all reason about is that despite me NOT being some sort of luddite who's afraid of change or whatever insult gets thrown around, my experiences with these tools keep getting tossed aside, and I mean by people who have a direct effect on my continued employment and lack of starvation. You're doing it yourself. We are literally looking at a prime of example of the problem, from THE BIGGEST PUSHERS of this tool, with many people in this thread and the reddit thread commenting similar things to myself, and it's being thrown to the wayside as an \"anecdote getting blown out of proportion\".What the fuck will it take for the AI pushers to finally stop moving the god damn goal posts and trying to spin every single failure presented to us in broad daylight as a \"you're le holding it le wrong teehee\" type of thing? Do we need to suffer through 20 million more slop PRs that accomplish nothing and STILL REQUIRE HUMAN HANDHOLDING before the sycophants relent a bit?\n- I have tried dumb chat modes (which IME still work the best somehow). The APIs rather than the UIs. Agent modes. \"Architect\" modes. I have given these tools free reign of my CLI to do whatever the fuck they wanted. Web search.- I have tried giving them the most comprehensive prompts imaginable. The type of prompts that, if you were to just give it to an intern, it'd be a truly miraculous feat of idiocy to fuck it up. I have tried having different AI models generate prompts for other AI models. I have tried compressing my entire codebase with tools like Repomix. I have tried only ever doing a single back-and-forth, as well as extremely deep chat chains hundreds of messages deep. Half the time my lazy \"nah that's shit do it again\" type of prompts work better than the detailed ones.- I have tried giving them instructions via JSON, TOML, YAML, Plaintext, Markdown, MDX, HTML, XML. I've tried giving them diagrams, mermaid charts, well commented code, well tested and covered code.Time after time after time, my experiences are pretty much a 1:1 match to what we're seeing in these PRs we're discussing. Absolute wastes of time and massive failures for anything that involves literally any complexity whatsoever. I have at this point wasted several orders of magnitudes more time trying to get AIs to spit out anything usable than if I had just sat down and done things myself. Yes, they save time for some specific tasks. I love that I can give it a big ass JSON blob and tell it to extract the typedef for me and it saves me 20 minutes of very tedious work (assuming it doesn't just make random shit up from time to time, which happens ~30% of the time still). I love that if there's some unimportant script I need to cook up real quick, I can just ask it and toss it away after I'm done.However, what I'm pissed beyond all reason about is that despite me NOT being some sort of luddite who's afraid of change or whatever insult gets thrown around, my experiences with these tools keep getting tossed aside, and I mean by people who have a direct effect on my continued employment and lack of starvation. You're doing it yourself. We are literally looking at a prime of example of the problem, from THE BIGGEST PUSHERS of this tool, with many people in this thread and the reddit thread commenting similar things to myself, and it's being thrown to the wayside as an \"anecdote getting blown out of proportion\".What the fuck will it take for the AI pushers to finally stop moving the god damn goal posts and trying to spin every single failure presented to us in broad daylight as a \"you're le holding it le wrong teehee\" type of thing? Do we need to suffer through 20 million more slop PRs that accomplish nothing and STILL REQUIRE HUMAN HANDHOLDING before the sycophants relent a bit?\n- I have tried giving them the most comprehensive prompts imaginable. The type of prompts that, if you were to just give it to an intern, it'd be a truly miraculous feat of idiocy to fuck it up. I have tried having different AI models generate prompts for other AI models. I have tried compressing my entire codebase with tools like Repomix. I have tried only ever doing a single back-and-forth, as well as extremely deep chat chains hundreds of messages deep. Half the time my lazy \"nah that's shit do it again\" type of prompts work better than the detailed ones.- I have tried giving them instructions via JSON, TOML, YAML, Plaintext, Markdown, MDX, HTML, XML. I've tried giving them diagrams, mermaid charts, well commented code, well tested and covered code.Time after time after time, my experiences are pretty much a 1:1 match to what we're seeing in these PRs we're discussing. Absolute wastes of time and massive failures for anything that involves literally any complexity whatsoever. I have at this point wasted several orders of magnitudes more time trying to get AIs to spit out anything usable than if I had just sat down and done things myself. Yes, they save time for some specific tasks. I love that I can give it a big ass JSON blob and tell it to extract the typedef for me and it saves me 20 minutes of very tedious work (assuming it doesn't just make random shit up from time to time, which happens ~30% of the time still). I love that if there's some unimportant script I need to cook up real quick, I can just ask it and toss it away after I'm done.However, what I'm pissed beyond all reason about is that despite me NOT being some sort of luddite who's afraid of change or whatever insult gets thrown around, my experiences with these tools keep getting tossed aside, and I mean by people who have a direct effect on my continued employment and lack of starvation. You're doing it yourself. We are literally looking at a prime of example of the problem, from THE BIGGEST PUSHERS of this tool, with many people in this thread and the reddit thread commenting similar things to myself, and it's being thrown to the wayside as an \"anecdote getting blown out of proportion\".What the fuck will it take for the AI pushers to finally stop moving the god damn goal posts and trying to spin every single failure presented to us in broad daylight as a \"you're le holding it le wrong teehee\" type of thing? Do we need to suffer through 20 million more slop PRs that accomplish nothing and STILL REQUIRE HUMAN HANDHOLDING before the sycophants relent a bit?\n- I have tried giving them instructions via JSON, TOML, YAML, Plaintext, Markdown, MDX, HTML, XML. I've tried giving them diagrams, mermaid charts, well commented code, well tested and covered code.Time after time after time, my experiences are pretty much a 1:1 match to what we're seeing in these PRs we're discussing. Absolute wastes of time and massive failures for anything that involves literally any complexity whatsoever. I have at this point wasted several orders of magnitudes more time trying to get AIs to spit out anything usable than if I had just sat down and done things myself. Yes, they save time for some specific tasks. I love that I can give it a big ass JSON blob and tell it to extract the typedef for me and it saves me 20 minutes of very tedious work (assuming it doesn't just make random shit up from time to time, which happens ~30% of the time still). I love that if there's some unimportant script I need to cook up real quick, I can just ask it and toss it away after I'm done.However, what I'm pissed beyond all reason about is that despite me NOT being some sort of luddite who's afraid of change or whatever insult gets thrown around, my experiences with these tools keep getting tossed aside, and I mean by people who have a direct effect on my continued employment and lack of starvation. You're doing it yourself. We are literally looking at a prime of example of the problem, from THE BIGGEST PUSHERS of this tool, with many people in this thread and the reddit thread commenting similar things to myself, and it's being thrown to the wayside as an \"anecdote getting blown out of proportion\".What the fuck will it take for the AI pushers to finally stop moving the god damn goal posts and trying to spin every single failure presented to us in broad daylight as a \"you're le holding it le wrong teehee\" type of thing? Do we need to suffer through 20 million more slop PRs that accomplish nothing and STILL REQUIRE HUMAN HANDHOLDING before the sycophants relent a bit?\nTime after time after time, my experiences are pretty much a 1:1 match to what we're seeing in these PRs we're discussing. Absolute wastes of time and massive failures for anything that involves literally any complexity whatsoever. I have at this point wasted several orders of magnitudes more time trying to get AIs to spit out anything usable than if I had just sat down and done things myself. Yes, they save time for some specific tasks. I love that I can give it a big ass JSON blob and tell it to extract the typedef for me and it saves me 20 minutes of very tedious work (assuming it doesn't just make random shit up from time to time, which happens ~30% of the time still). I love that if there's some unimportant script I need to cook up real quick, I can just ask it and toss it away after I'm done.However, what I'm pissed beyond all reason about is that despite me NOT being some sort of luddite who's afraid of change or whatever insult gets thrown around, my experiences with these tools keep getting tossed aside, and I mean by people who have a direct effect on my continued employment and lack of starvation. You're doing it yourself. We are literally looking at a prime of example of the problem, from THE BIGGEST PUSHERS of this tool, with many people in this thread and the reddit thread commenting similar things to myself, and it's being thrown to the wayside as an \"anecdote getting blown out of proportion\".What the fuck will it take for the AI pushers to finally stop moving the god damn goal posts and trying to spin every single failure presented to us in broad daylight as a \"you're le holding it le wrong teehee\" type of thing? Do we need to suffer through 20 million more slop PRs that accomplish nothing and STILL REQUIRE HUMAN HANDHOLDING before the sycophants relent a bit?\nHowever, what I'm pissed beyond all reason about is that despite me NOT being some sort of luddite who's afraid of change or whatever insult gets thrown around, my experiences with these tools keep getting tossed aside, and I mean by people who have a direct effect on my continued employment and lack of starvation. You're doing it yourself. We are literally looking at a prime of example of the problem, from THE BIGGEST PUSHERS of this tool, with many people in this thread and the reddit thread commenting similar things to myself, and it's being thrown to the wayside as an \"anecdote getting blown out of proportion\".What the fuck will it take for the AI pushers to finally stop moving the god damn goal posts and trying to spin every single failure presented to us in broad daylight as a \"you're le holding it le wrong teehee\" type of thing? Do we need to suffer through 20 million more slop PRs that accomplish nothing and STILL REQUIRE HUMAN HANDHOLDING before the sycophants relent a bit?\nWhat the fuck will it take for the AI pushers to finally stop moving the god damn goal posts and trying to spin every single failure presented to us in broad daylight as a \"you're le holding it le wrong teehee\" type of thing? Do we need to suffer through 20 million more slop PRs that accomplish nothing and STILL REQUIRE HUMAN HANDHOLDING before the sycophants relent a bit?\n\n\n\nFirst marketing gaslighting from the faangs and hot startups with grifters that managed to raise and need to keep the bullshit windmill going.Second is that these tools are relatively the best in boilerplate nextjs code that the vibecoders use to make a very simple dashboard and stuff, and they're the noisy minority on twitter.There is basically zero financial incentive to admit LLM's are pushed dangerously beyond their current limits. I'm still figuring a way to go short this, apart from literally shorting the market.\nSecond is that these tools are relatively the best in boilerplate nextjs code that the vibecoders use to make a very simple dashboard and stuff, and they're the noisy minority on twitter.There is basically zero financial incentive to admit LLM's are pushed dangerously beyond their current limits. I'm still figuring a way to go short this, apart from literally shorting the market.\nThere is basically zero financial incentive to admit LLM's are pushed dangerously beyond their current limits. I'm still figuring a way to go short this, apart from literally shorting the market.\n\n\n\nThen we have the current batch of YC execs heavily pushing \"vibe coded\" startups. The sad reality is that this strategy will probably work because all they need is the next incredulous business guy to buy the vibe coded startup. There's so much money in the AI space to the point where I fully believe you can likely make billions of dollars this way through acquisition (see OAI buying Windsurf for billions of dollars, likely to devalue Cursor's also absurd valuation).I'm not a luddite. I'm a huge fan of companies spending a decent chunk of money on R&D on innovative new projects even when there's a high risk of failure. The current LLM hype is not just an R&D project anymore. This is now being pushed as a full on replacement of human labor when it's clearly not ready. And now we're valuing AI startups at billions of dollars and planning to spend $500B on AI infrastructure so that we can generate more ghibli memes.At some point this has to stop but I'm afraid by that point the damage will already be done. Even worse, the idiots who led this exercise in massive waste will just hop onto the next hype train.\nI'm not a luddite. I'm a huge fan of companies spending a decent chunk of money on R&D on innovative new projects even when there's a high risk of failure. The current LLM hype is not just an R&D project anymore. This is now being pushed as a full on replacement of human labor when it's clearly not ready. And now we're valuing AI startups at billions of dollars and planning to spend $500B on AI infrastructure so that we can generate more ghibli memes.At some point this has to stop but I'm afraid by that point the damage will already be done. Even worse, the idiots who led this exercise in massive waste will just hop onto the next hype train.\nAt some point this has to stop but I'm afraid by that point the damage will already be done. Even worse, the idiots who led this exercise in massive waste will just hop onto the next hype train.\n\n\n\n\n\n\n\n\n\n\n\n\nAI is aimed at eliminating the jobs of most of HN so it's understandable that HN doesn't want AI to succeed at its goal.\n\n\n\nIt is difficult for ceo/management to understand that the ai tools dont work when their salary depends on them working since they have invested billions into it.\n\n\n\n\n\n\n\n\n\n\n\n\nHe also said in the video:> I brought a rocket company because it was like interesting. And it's an area that I'm not an expert in and I wanted to be a expert. So I'm using Deep Research (TM). And these systems are spending 10 minutes writing Deep Papers (TM) that's true for most of them. (Them he starts to talk about computation and \"it typically speaks English language\", very cohesively, then stopped the thread abruptly) (Timestamp 02:09)Let me quote out the important in what he said: \"it's an area that I'm not an expert in\".During my use of AI (yeah, I don't hate AI), I found that the current generative (I call them pattern reconstruction) systems has this great ability to Impress An Idiot. If you have no knowledge in the field, you maybe thinking the generated content is smart, until you've gained some depth enough to make you realize the slops hidden in it.If you work at the front line, like those guys from Microsoft, of course you know exactly what should be done, but, the company leadership maybe consists of idiots like Eric who got impressed by AI's ability to choose smart sounding words without actually knowing if the words are correct.I guess maybe one day the generative tech could actually write some code that is correct and optimal, but right now it seems that day is far from now.\n> I brought a rocket company because it was like interesting. And it's an area that I'm not an expert in and I wanted to be a expert. So I'm using Deep Research (TM). And these systems are spending 10 minutes writing Deep Papers (TM) that's true for most of them. (Them he starts to talk about computation and \"it typically speaks English language\", very cohesively, then stopped the thread abruptly) (Timestamp 02:09)Let me quote out the important in what he said: \"it's an area that I'm not an expert in\".During my use of AI (yeah, I don't hate AI), I found that the current generative (I call them pattern reconstruction) systems has this great ability to Impress An Idiot. If you have no knowledge in the field, you maybe thinking the generated content is smart, until you've gained some depth enough to make you realize the slops hidden in it.If you work at the front line, like those guys from Microsoft, of course you know exactly what should be done, but, the company leadership maybe consists of idiots like Eric who got impressed by AI's ability to choose smart sounding words without actually knowing if the words are correct.I guess maybe one day the generative tech could actually write some code that is correct and optimal, but right now it seems that day is far from now.\nLet me quote out the important in what he said: \"it's an area that I'm not an expert in\".During my use of AI (yeah, I don't hate AI), I found that the current generative (I call them pattern reconstruction) systems has this great ability to Impress An Idiot. If you have no knowledge in the field, you maybe thinking the generated content is smart, until you've gained some depth enough to make you realize the slops hidden in it.If you work at the front line, like those guys from Microsoft, of course you know exactly what should be done, but, the company leadership maybe consists of idiots like Eric who got impressed by AI's ability to choose smart sounding words without actually knowing if the words are correct.I guess maybe one day the generative tech could actually write some code that is correct and optimal, but right now it seems that day is far from now.\nDuring my use of AI (yeah, I don't hate AI), I found that the current generative (I call them pattern reconstruction) systems has this great ability to Impress An Idiot. If you have no knowledge in the field, you maybe thinking the generated content is smart, until you've gained some depth enough to make you realize the slops hidden in it.If you work at the front line, like those guys from Microsoft, of course you know exactly what should be done, but, the company leadership maybe consists of idiots like Eric who got impressed by AI's ability to choose smart sounding words without actually knowing if the words are correct.I guess maybe one day the generative tech could actually write some code that is correct and optimal, but right now it seems that day is far from now.\nIf you work at the front line, like those guys from Microsoft, of course you know exactly what should be done, but, the company leadership maybe consists of idiots like Eric who got impressed by AI's ability to choose smart sounding words without actually knowing if the words are correct.I guess maybe one day the generative tech could actually write some code that is correct and optimal, but right now it seems that day is far from now.\nI guess maybe one day the generative tech could actually write some code that is correct and optimal, but right now it seems that day is far from now.\n\n\n\nWhen I use AI, I keep it on a short leash.Meanwhile, folks like this (\"I bought a rocket company\") are essentially using it to decide where to plough their stratospheric wealth, so they can grow it even further.Perhaps they'll lose a cufflink in the eventual crash, but they're so rich, I don't think they'll lose their shirt. Meanwhile, the tech job market is f**ed either way.\nMeanwhile, folks like this (\"I bought a rocket company\") are essentially using it to decide where to plough their stratospheric wealth, so they can grow it even further.Perhaps they'll lose a cufflink in the eventual crash, but they're so rich, I don't think they'll lose their shirt. Meanwhile, the tech job market is f**ed either way.\nPerhaps they'll lose a cufflink in the eventual crash, but they're so rich, I don't think they'll lose their shirt. Meanwhile, the tech job market is f**ed either way.\n\n\n\nKudos to you for having the strength to get through it, and for living to tell the tale!\n\n\n\n> idiots like EricNow imagine Google working with US military putting Gemini into a fleet of autonomous military drones with machine guns.\nNow imagine Google working with US military putting Gemini into a fleet of autonomous military drones with machine guns.\n\n\n\nLiterally the killer app of AI.\n\n\n\n\n\n\nI would be genuinely positively surprised if that stops to be the case some day. This behavior is by design.AS you put yourself, these LLM systems are very good at pattern recognition and reconstruction. They have ingested vast majority of the internet to build patterns on. On the internet, the absolutely vast majority of content is pushed out by novices and amateurs: \"Hey, look, I have just read a single wikipedia page or attended single lesson, I am not completely dumbfounded by it, so now I will explain it to you\".LLMs have to be peak Dunning-Krugers - by design.\nAS you put yourself, these LLM systems are very good at pattern recognition and reconstruction. They have ingested vast majority of the internet to build patterns on. On the internet, the absolutely vast majority of content is pushed out by novices and amateurs: \"Hey, look, I have just read a single wikipedia page or attended single lesson, I am not completely dumbfounded by it, so now I will explain it to you\".LLMs have to be peak Dunning-Krugers - by design.\nLLMs have to be peak Dunning-Krugers - by design.\n\n\n\n\n\n\n\n\n\n\n\n\nJust because some people on reddit 'laugh' at these discussions, in one of the PRs a contributor/maintainer actually said that they enabled it on purpose, are not forced and are happy to test it out.And someone somewhere has and want to test stuff. Whats the issue? Test it out, play around with it, keep it or disable it.And i think .net as a repository is a very good example. The people on github copilot side are probably very happy about this experiement. For me its also great, it seems like github copilot is still struggling a bit.And copilot is called copilot because they do not advertice it as replacement.\nAnd someone somewhere has and want to test stuff. Whats the issue? Test it out, play around with it, keep it or disable it.And i think .net as a repository is a very good example. The people on github copilot side are probably very happy about this experiement. For me its also great, it seems like github copilot is still struggling a bit.And copilot is called copilot because they do not advertice it as replacement.\nAnd i think .net as a repository is a very good example. The people on github copilot side are probably very happy about this experiement. For me its also great, it seems like github copilot is still struggling a bit.And copilot is called copilot because they do not advertice it as replacement.\nAnd copilot is called copilot because they do not advertice it as replacement.\n\n\n\n\n\n\nI created images and music which was enjoyable. I use it to add more progress to an indie side project I'm playing around with (i added more functionality to it with ai stuff like claude code and now jules.google than i did myself in the last 3 years).It helps my juniors to become better in their jobs.Everything related to sound / talking to a computer is now solved. I talked to gemini yesterday and i interruptted it.Image segmentation became a solved problem and that was really hard before.I can continue my list of things AI/ML made things possible in the last few years which were impossible before that.\nIt helps my juniors to become better in their jobs.Everything related to sound / talking to a computer is now solved. I talked to gemini yesterday and i interruptted it.Image segmentation became a solved problem and that was really hard before.I can continue my list of things AI/ML made things possible in the last few years which were impossible before that.\nEverything related to sound / talking to a computer is now solved. I talked to gemini yesterday and i interruptted it.Image segmentation became a solved problem and that was really hard before.I can continue my list of things AI/ML made things possible in the last few years which were impossible before that.\nImage segmentation became a solved problem and that was really hard before.I can continue my list of things AI/ML made things possible in the last few years which were impossible before that.\nI can continue my list of things AI/ML made things possible in the last few years which were impossible before that.\n\n\n\n\n\n\nAnd didn’t actually provide light, but everyone on 19th century twitter says that it will one day provide light if you believe hard enough, so you should rip out your gas lamps and install it now.Like, this is just generation of useless busy-work, as far as I can see; it is clearly worse than useless. The PRs don't even have passing CI!\nLike, this is just generation of useless busy-work, as far as I can see; it is clearly worse than useless. The PRs don't even have passing CI!\n\n\n\nFor refactoring and extending good, working code, AI is much more useful.We are at a stage where AI should only be used for giving suggestions to a human in the driver's seat with a UI/UX that allows ergonomically guiding the AI, picking from offered alternatives, giving directions on a fairly micro level that is still above editing the code character by character.They are indeed overpromising and pushing AI beyond its current limits for hype reasons, but this doesn't mean this won't be possible in the future. The progress is real, and I wouldn't bet on it taking a sharp turn and flattening.\nWe are at a stage where AI should only be used for giving suggestions to a human in the driver's seat with a UI/UX that allows ergonomically guiding the AI, picking from offered alternatives, giving directions on a fairly micro level that is still above editing the code character by character.They are indeed overpromising and pushing AI beyond its current limits for hype reasons, but this doesn't mean this won't be possible in the future. The progress is real, and I wouldn't bet on it taking a sharp turn and flattening.\nThey are indeed overpromising and pushing AI beyond its current limits for hype reasons, but this doesn't mean this won't be possible in the future. The progress is real, and I wouldn't bet on it taking a sharp turn and flattening.\n\n\n", "sentiment": "positive", "sentiment_score": 1.0, "summary": "News article: Watching AI drive Microsoft employees insane | Hacker News", "source": "google_search", "date": "2025-06-18T02:51:04.861597Z"}, {"headline": "Microsoft Fabric Blog", "description": "Keep up with the latest Microsoft Fabric updates, announcements, information, & new features on the Microsoft Fabric blog. Search by category or date published.", "url": "https://blog.fabric.microsoft.com/", "image_url": "https://blog.fabric.microsoft.com/images/shared/social/social-image-v2.png", "full_content": "\r\n                            June 2, 2025 by\r\n                            <PERSON>\n20,281 Views\n\nThe Microsoft Fabric Community Conference is back for its third year—and we’re bringing everything and everybody you’ve loved at past events with us to Atlanta, Georgia. After unforgettable experiences at FabCon in Las Vegas and Stockholm, the Fabric community proved just how powerful it can be when we come together. With more than 13,000 attendees across our last three conferences, it’s clear: the Microsoft Fabric community is here to drive the future of data!    And yes, we’re pleased to announce; it’s happening again! Mark your calendars … \nContinue reading “Microsoft Fabric Community Conference Comes to Atlanta!”\nCo-author: <PERSON>, Data Scientist Manager Overview  As organizations increasingly rely on real-time data to drive decisions, the need for intelligent, responsive systems has never been greater. At the heart of this transformation is Fabric Real-Time Intelligence (RTI), a platform that empowers users to act on data as it arrives. Today, we’re excited to announce … \nContinue reading “Introducing MCP Support for Real-Time Intelligence (RTI) “\nThe Eventstreams artifact in the Microsoft Fabric Real-Time Intelligence experience lets you bring real-time events into Fabric, transform them, and then route them to various destinations such as Eventhouse, without writing any code (no-code). You can ingest data from an Eventstream to Eventhouse seamlessly either from Eventstream artifact or Eventhouse Get Data Wizard. This capability … \nContinue reading “Fabric Eventhouse now supports Eventstream Derived Streams in Direct Ingestion mode (Preview)”\nWe’re excited to announce Surge Protection for background operations is now Generally Available (GA). Using surge protection, capacity admins can limit overuse by background operations in their capacities. \nResult Set Caching is now available in preview for Microsoft Fabric Data Warehouse and Lakehouse SQL analytics endpoint. This performance optimization works transparently to cache the results of eligible T-SQL queries. When the same query is issued again, it directly retrieves the stored result, instead of recompiling and recomputing the original query. This operation drastically … \nContinue reading “Result Set Caching for Microsoft Fabric Data Warehouse (Preview)”\nIntroduction Integrating Azure API Management (APIM) with Microsoft Fabric’s API for GraphQL can significantly enhance your API’s capabilities by providing robust scalability and security features such as identity management, rate limiting, and caching. This post will guide you through the process of setting up and configuring these features. You may not be familiar with API … \nContinue reading “Integrating Azure API Management with Fabric API for GraphQL”\n\r\n                    Accelerate your data potential with a unified analytics solution that connects it all. Microsoft Fabric enables you to manage your data in one place with a suite of analytics experiences that seamlessly work together, all hosted on a lake-centric SaaS solution for simplicity and to maintain a single source of truth.\r\n                \n Get the latest news from Microsoft Fabric Blog\nThis will prompt you to login with your Microsoft account to subscribe", "sentiment": "positive", "sentiment_score": 0.9953, "summary": "News article: Microsoft Fabric Blog", "source": "google_search", "date": "2025-06-18T02:51:06.128213Z"}, {"headline": "", "description": "", "url": "https://finance.yahoo.com/quote/MSFT/", "image_url": "", "full_content": "", "sentiment": "neutral", "sentiment_score": 0.0, "summary": "News article: ", "source": "google_search", "date": "2025-06-18T02:51:06.541122Z"}, {"headline": "403 Forbidden", "description": "", "url": "https://blogs.microsoft.com/blog/2025/05/19/microsoft-build-2025-the-age-of-ai-agents-and-building-the-open-agentic-web/", "image_url": "", "full_content": "", "sentiment": "negative", "sentiment_score": -0.4215, "summary": "News article: 403 Forbidden", "source": "google_search", "date": "2025-06-18T02:51:06.959393Z"}, {"headline": "\n\tVisual Studio Hub: Your one-stop destination for latest news, updates, and insights", "description": "Stay up-to-date with everything Visual Studio! Get the latest news, features, tips, and resources to elevate your development experience.", "url": "https://visualstudio.microsoft.com/hub/", "image_url": "https://visualstudio.microsoft.com/wp-content/uploads/2025/02/vs-hub-seo-img.jpg", "full_content": "Your one-stop destination for everything Visual Studio\n\nWhat’s new\nWhether you’re using Visual Studio for the first time or you’ve been using it for years, there’s a lot to like in our newest version.\n\nGitHub Copilot\nShip great software faster, get contextualized assistance throughout your workflow.\nDiscover how\n\nVisual Studio Events\nBuild valuable connections with fellow developers and industry experts at our upcoming Visual Studio events.\nGet connected\n\nKey resources\nUnlock the knowledge and support you need to grow as a developer.\nDive in\n\nDeveloper Blogs\nStay ahead of the curve by keeping up with the latest trends and best practices.\nGet highlights\n\nSocial channels\nConnect with us on social media to stay updated with the latest news, tips, and exclusive content.\nCheck out what's new in the latest release of Visual Studio 17.14.\nGitHub Copilot Walkthroughs are now available in Visual Studio to help you get up and running quickly. Walkthroughs guide you through using key Copilot features directly in your editor.\nYou can now let Copilot fully implement your empty C# method.\nIntroducing the GPT-4o Copilot code completion model to bring you higher quality completions.\nNew Mono debug engine for .NET MAUI integrated into the Visual Studio Core debugger.\nEnhanced LINQ expression debugging experience with clause hovering datatip.\nNES leverages the previous edits made and predicts the next edit to come, whether it’s an insertion, deletion, or mix of both.\nAutomatically generate doc comments for C++ and C# functions.\nThe .NET Allocation Tool now identifies zero-length array allocations, helping optimize memory usage and performance.\nGet the newest GitHub Copilot updates, expert tips, and coding insights for Visual Studio to enhance your coding speed, accuracy, and efficiency.\nNo trial. No Credit card required. Just your GitHub account.\nGitHub Copilot WalkthroughGitHub Copilot Walkthroughs are now available in Visual Studio to help you get up and running quickly. Walkthroughs guide you...Latest updateNext Edit SuggestionNES leverages the previous edits made and predicts the next edit to come, whether it's an insertion, deletion, or mix...Latest updateFaster .NET Upgrades Powered by GitHub CopilotIntroducing GitHub Copilot app modernization – Upgrade for .NET You probably don’t look forward to the process of modernizing your...Expert tip🎉 Visual Studio 2022 v17.14 is now generally available!We’re thrilled to announce the general availability of Visual Studio 2022 version 17.14! This release continues our mission to empower...Expert tipAI doc comment generationAutomatically generate doc comments for C++ and C# functions.Latest updateGPT-4o code completion modelIntroducing the GPT-4o Copilot code completion model to bring you higher quality completions.Latest updateImplement with CopilotYou can now let Copilot fully implement your empty C# method.Latest updateNext edit suggestions available in Visual Studio GitHub CopilotGitHub Copilot code completions, or gray text, are specialized in autocompleting unfinished code or providing helpful template code. In reality,...Expert tipAgent mode has arrived in preview for Visual StudioPlan, build, test, and fix — all from one prompt. Agent mode is now available in public preview for all...Expert tipHow to Watch Microsoft Build 2025 OnlineMicrosoft Build 2025 Is Here! — How to Make the Most of It Virtually It’s that time again—Microsoft Build has...Expert tip\n\n\n\n\n\n\n\n\n\n\n\n\nGitHub Copilot Walkthroughs are now available in Visual Studio to help you get up and running quickly. Walkthroughs guide you...\nNES leverages the previous edits made and predicts the next edit to come, whether it's an insertion, deletion, or mix...\nIntroducing GitHub Copilot app modernization – Upgrade for .NET You probably don’t look forward to the process of modernizing your...\nWe’re thrilled to announce the general availability of Visual Studio 2022 version 17.14! This release continues our mission to empower...\nAutomatically generate doc comments for C++ and C# functions.\nIntroducing the GPT-4o Copilot code completion model to bring you higher quality completions.\nYou can now let Copilot fully implement your empty C# method.\nGitHub Copilot code completions, or gray text, are specialized in autocompleting unfinished code or providing helpful template code. In reality,...\nPlan, build, test, and fix — all from one prompt. Agent mode is now available in public preview for all...\nMicrosoft Build 2025 Is Here! — How to Make the Most of It Virtually It’s that time again—Microsoft Build has...\n \nSubscribe now\nUnable to load blog posts.\nStay ahead in your developer journey with events and opportunities to connect with fellow\ndevelopers, share insight, and find inspiration.\nLearn how to modernize your .NET applications and unlock the benefits of modern app development in this virtual event on April 22 and 23, 2025.\nCalling all developers, creators, and the AI innovators to join us in Seattle and refactor your skills, May 19-22\nVSLive! returns to Microsoft headquarters for five days of in-depth training, networking, and fun! Join us for hands-on and lecture-based workshops, in-depth sessions, keynotes, and more.\nJoin VSLive! in San Diego for five days of sun, fun, and intense developer training. Expect industry experts and Microsoft product team members to share their knowledge.\nLearn how to modernize your .NET applications and unlock the benefits of modern app development in this virtual event on April 22 and 23, 2025.\nCalling all developers, creators, and the AI innovators to join us in Seattle and refactor your skills, May 19-22\nVSLive! returns to Microsoft headquarters for five days of in-depth training, networking, and fun! Join us for hands-on and lecture-based workshops, in-depth sessions, keynotes, and more.\nJoin VSLive! in San Diego for five days of sun, fun, and intense developer training. Expect industry experts and Microsoft product team members to share their knowledge.\n \nEmpower your development journey with the right knowledge and tools.\nDive into our in-depth documentation to discover best practices, feature tutorials, and more—a destination perfect for beginners and experienced users.\nStay ahead in tech and get the job done with Microsoft’s developer tools and services. Grow your skillset and expand your network with local meetups and events.\nHelp shape the future of Visual Studio by connecting directly with Microsoft engineers on Developer Community.\nThe Visual Studio Hub is a one-stop destination for all things Visual Studio, including the latest blogs, release notes, YouTube videos, social media updates, and community discussions.\n\n\t\t\t\t\t\tFeedback \n", "sentiment": "positive", "sentiment_score": 0.9982, "summary": "News article: \n\tVisual Studio Hub: Your one-stop destination for latest news, updates, and insights", "source": "google_search", "date": "2025-06-18T02:51:07.409943Z"}, {"headline": "403 Forbidden", "description": "", "url": "https://blogs.microsoft.com/on-the-issues/", "image_url": "", "full_content": "", "sentiment": "negative", "sentiment_score": -0.4215, "summary": "News article: 403 Forbidden", "source": "google_search", "date": "2025-06-18T02:51:07.760552Z"}, {"headline": "403 Forbidden", "description": "", "url": "https://ukstories.microsoft.com/recent-news/", "image_url": "", "full_content": "", "sentiment": "negative", "sentiment_score": -0.4215, "summary": "News article: 403 Forbidden", "source": "google_search", "date": "2025-06-18T02:51:08.874030Z"}, {"headline": "What's new in Microsoft Intune | Microsoft Learn", "description": "Find out what's new in Microsoft Intune.", "url": "https://learn.microsoft.com/en-us/intune/intune-service/fundamentals/whats-new", "image_url": "https://learn.microsoft.com/en-us/media/open-graph-image.png", "full_content": "This browser is no longer supported.\n\n\t\t\t\t\t\tUpgrade to Microsoft Edge to take advantage of the latest features, security updates, and technical support.\n\t\t\t\t\t\n\n\n\n\nNote\n\n\n\t\t\t\t\t\tAccess to this page requires authorization. You can try signing in or changing directories.\n\t\t\t\t\t\n\n\t\t\t\t\t\tAccess to this page requires authorization. You can try changing directories.\n\t\t\t\t\t\nLearn what's new each week in Microsoft Intune.\nYou can also read:\nNote\nEach monthly update can take up to three days to roll out and will be in the following order:\nSome features roll out over several weeks and might not be available to all customers in the first week.\nFor a list of upcoming Intune feature releases, see In development for Microsoft Intune.\nFor new information about Windows Autopilot solutions, see:\nYou can use RSS to be notified when this page is updated. For more information, see How to use the docs.\nWhen adding a Win32 app to Intune, you can select an option to check and install the app on Windows devices running ARM64 operating systems. This capability is available from the Microsoft Intune admin center by selecting Apps > All apps > Create. The ARM64 option is available by selecting the Operating system architecture option under the Requirements step. To ensure that you don't have any impact to any Win32 applications that you previously targeted to 64-bit devices, your existing 64-bit Win32 applications will also have ARM64 selected. After the availability of being able to specifically target ARM64 operating system architectures, selecting x64 will not target ARM64 devices.\nFor related information, see Win32 app management in Microsoft Intune.\nApplies to:\nThe Vulnerability Remediation Agent is currently in a limited public preview and available to only a select group of customers. If you’re interested in gaining access or would like to learn more, please reach out to your sales team for further details and next steps.\nWhen run, this agent uses data from Microsoft Defender Vulnerability Management to identify and then provide remediation guidance for vulnerabilities on your managed devices. You run and access the agent and view its results from within the Intune admin center where you’ll see suggestions prioritized by the agent for remediation. Each suggestion includes key information like associated CVEs, severity, exploitability, affected systems, organizational exposure, business impact, and remediation guidance.\nThis information empowers you with a current assessment of potential risk to your environment and guidance to help you decide which risk to address first.\nFor more information about this agent including prerequisites, see Vulnerability Remediation Agent for Security Copilot in Microsoft Intune.\nEndpoint Privilege Management (EPM) elevation rules now include a new file elevation type of Deny. An EPM elevation rule set to Deny blocks the specified file from running in an elevated context. While we recommend using file elevation rules to allow users to elevate specific files, a deny rule can help you ensure that certain files like known and potentially malicious software can't be run in an elevated context.\nDeny rules support the same configuration options as other elevation types except for child processes, which are not used.\nFor more information about EPM, which is available as an Intune Suite add-on-capability, see Endpoint Privilege Management overview.\nThe following protected apps are now available for Microsoft Intune:\nFor more information about protected apps, see Microsoft Intune protected apps.\nWe've added support to use DFCI profiles to manage UEFI (BIOS) settings for NEC devices that run Windows 10 or Windows 11. Not all NEC devices running Windows are enabled for DFCI. Contact your device vendor or device manufacturer for eligible devices.\nYou can manage DFCI profiles from within the Microsoft Intune admin center by going to Devices > Manage devices > Configuration > Create > New policy > Windows 10 and later for platform > Templates > Device Firmware Configuration Interface for profile type. For more information about DFCI profiles, see:\nApplies to:\nUse a custom template for naming AOSP user-affiliated and userless devices when they enroll with Intune. The template is available to configure in the enrollment profile. It can contain a combination of free text and predefined variables, such as device serial number, device type, and for user-affiliated devices, the owner's username. For more information about how to configure the template, see:\nWe updated role-based access control (RBAC) for device limits. If you're currently assigned the policy and profile manager role, or the device configurations permissions that are built-in to the role, you now have read-only access to device enrollment limit policies. To create and edit these policies, you must be an Intune  Administrator.\nAndroid, iOS and Mac devices are added to device inventory. Intune now collects a default set of inventory data including 74 Apple properties and 32 Android properties.\nFor more information, see View device details with Microsoft Intune.\nDuring an unattended Remote Help sessions on Android devices, we've enhanced the security and user awareness during remote assistance by blocking the screen of the device, and notifying users if they interact with it.\nThis feature is for Zebra and Samsung devices that enrolled as Android Enterprise corporate owned dedicated devices.\nFor more information on Remote Help, see Remote Help.\nConfigure compliance policies to detect if a corporate-owned Android Enterprise device is rooted. If Microsoft Intune detects that a device is rooted, you can have it marked as noncompliant. This feature is now available for devices enrolled as fully managed, dedicated, or corporate-owned with a work profile. For more information, see Device compliance settings for Android Enterprise in Intune.\nApplies to:\nAs part of the Intune scenario for Microsoft Defender for Endpoint security settings management, you can use a new Endpoint detection and response profile for Linux named Microsoft Defender Global Exclusions (AV+EDR) that you can now use to manage Linux device exclusions for both Microsoft Defender Endpoint detection and response (EDR) and Antivirus (AV).\nThis profile supports settings related to global exclusion settings as detailed in Configure and validate exclusions on Linux in the Microsoft Defender documentation. These exclusion configurations can apply to both the antivirus and EDR engines on the Linux client to stop associated real time protection EDR alerts for excluded items. Exclusions can be defined by the file path, folder, or process explicitly defined by the admin in the policy.\nThe new Intune profile:\nFor details about the available Defender settings, see Configure security settings in Microsoft Defender for Endpoint on Linux - Microsoft Defender for Endpoint in the Defender for Endpoint documentation.\nApplies to:\nYou can now collect data from the SimInfo entity on Windows devices with enhanced device inventory.\nFor more information, see Intune Data Platform.\nApplies to:\nWe've extended app protection policies (APP) to support Microsoft Edge (v136 or later), OneDrive (v16.8.4 or later), and Outlook (v4.2513.0 or later). To enable this setting for these specific apps on visionOS devices, you must set com.microsoft.intune.mam.visionOSAllowiPadCompatApps to Enabled in your app configuration policy. Once you have assigned your app configuration policy, you can create and assign your app protection policy for your VisionOS devices. For more information, see Protect data on VisionOS devices.\nMicrosoft Intune has a new icon. The Intune icon is being updated across platforms and apps associated with Intune, such as the Intune admin center and Intune Company Portal app. The new icon will gradually be implemented over the next few months.\nFile elevation rules for Endpoint Privilege Management (EPM) now support command line file arguments. When an elevation rule is configured to define one or more file arguments, EPM allows that file to run in an elevated request only when one of the defined arguments is used. EPM blocks elevation of the file should a command line argument be used that isn't defined by the elevation rule. Use of file arguments in your file elevation rules can help you refine how and for what intent different files are successfully run in an elevated context by Endpoint Privilege Management.\nEPM is available as an Intune Suite add-on-capability.\nThe relationship viewer provides a graphical depiction of the relationships between different applications in the system, including superseding and dependent applications. Admins can find relationship viewer in Intune by selecting Apps > All apps > a Win32 app > Relationship viewer. The relationship viewer supports both Win32 apps and Enterprise App Catalog apps. For more information, see App relationship viewer.\nApple recently updated the API for their volume purchase program (VPP), which is used to manage apps and books. Apple's related API is now version 2.0. Version 1.0 is deprecated. To support the Apple updates, Microsoft Intune has updated to use the new API, which is faster and more scalable than the previous version.\nApplies to:\nIntune now provides additional storage services options when saving copies of org data using an app protection policy for Android or iOS. In addition to the existing org data storage options, you can also select iManage and Egnyte as storage options. You must select these services as exemptions from your block list by setting Save copies of org data to Block, then selecting the allowed storage services next to the Allow user to save copies to selected services setting. Note that this setting does not apply to all applications.\nFor more information about data protection using app protection policies, see iOS app protection policy settings - Data protection and Android app protection policy settings - Data protection.\nApplies to:\nWe’ve updated the device configuration template for Windows Delivery Optimization. The new template uses the settings format as found in the Settings Catalog, with settings that are taken directly from the Windows Configuration Service Providers (CSPs) for Windows Delivery Optimization, as documented by Windows at Policy CSP – DeliveryOptimization.\nWith this change you can no longer create new versions of the old profile. However, your pre-existing instances of the old profile remain available to use.\nFor more information about this change, see the Intune Customer Success blog at Support tip: Windows device configuration policies migrating to unified settings platform in Intune.\nApplies to:\nThe Settings Catalog lists all the settings you can configure in a device policy, and all in one place. For more information about configuring Settings Catalog profiles in Intune, see Create a policy using settings catalog.\nWe've added a new setting in the Settings Catalog. To see this setting, in the Microsoft Intune admin center, see Devices > Manage devices > Configuration > Create > New policy > macOS for platform > Settings catalog for profile type.\nLogin > Login Window:\nThe settings catalog supports Android Enterprise and Android Open Source Project (AOSP).\nCurrently, to configure Android settings, you use the built-in templates. The settings from these templates are also available in the settings catalog. More settings will continue to be added.\nIn the Intune admin center, when you create a device configuration profile, you select the Profile Type (Devices > Manage devices > Configuration > Create > New policy > select your Platform > Profile Type). All the profile types are moved to Profile Type > Templates.\nThis change:\nIn the new settings catalog experience, the management mode associated with the setting is available in the tooltip.\nTo get started with settings catalog, see Use the settings catalog to configure settings on your devices.\nApplies to:\nYou can use a custom template for naming Android Enterprise corporate-owned devices when they enroll with Intune. The template is available to configure in the enrollment profile. It can contain a combination of custom text and predefined variables, such as device serial number, device type, and for user-affiliated devices, the owner's username. For more information, see:\nApplies to:\nNow available for Android Enterprise corporate-owned devices, enrollment time grouping enables you to assign a static Microsoft Entra group to devices at enrollment time. When a targeted Android device enrolls, it receives all assigned policies, apps, and settings, typically by the time the user lands on the home screen. You can configure one static Microsoft Entra group per enrollment profile under the Device group tab in the Microsoft Intune admin center. For more information, see Enrollment time grouping.\nStarting in April 2025, Intune no longer supports custom profiles for Android Enterprise personally owned work profile devices. With this end of support:\nAdmins won’t be able to create new custom profiles for personally owned work profile devices. However, admins can still view and edit previously created custom profiles.\nPersonally-owned work profile devices that currently have a custom profile assigned won't experience any immediate change of functionality. Because these profiles are no longer supported, the functionality set by these profiles might change in the future.\nIntune technical support no longer supports custom profiles for personally owned work profile devices.\nAll custom policies should be replaced with other policy types. Learn more about Intune ending support for personally owned work profile custom profiles\nNote\nRollout of the new settings for the security baseline is underway, but taking longer than usual. Due to this delay, the new settings might not be available until the week of May 5, 2025.\nThe most recent Intune security baseline for Windows, version 24H2, is updated to include 15 new settings for managing the Windows Configuration Service Provider (CSP) for Lanman Server and Lanman Workstation. These settings were previously unavailable in the baseline due to missing CSP support. The addition of these settings provides better control and configuration options.\nBecause this is an update to an existing baseline version and not a new baseline version, the new settings aren’t visible in the baselines properties until you edit and save the baseline:\nPre-existing baseline instances:\nBefore the new settings are available in a pre-existing baseline instance, you must select and then Edit that baseline instance. To have the baseline deploy the new settings, you must then Save that baseline instance. When the baseline is opened for editing, each of the new settings becomes visible with its default security baseline configuration. Before saving, you can reconfigure one or more of the new settings or make no changes other than to save the current configuration which then uses the baseline defaults for each of the new settings.\nNew baseline instances:\nWhen you create a new instance of a Windows security baseline version 24H2, that instance includes the new settings along with all the previously available settings.\nFollowing are the new settings that are added to the version 24H2 baseline, and the baseline default for each:\nLanman Server\nLanman Workstation\nFor more information, see Intune security baselines.\nThe following protected apps are now available for Microsoft Intune:\nFor more information about protected apps, see Microsoft Intune protected apps.\nMicrosoft Intune admin center's home page has been updated to include additional links to interactive demos, documentation, and training. To see these updates, navigate to the Microsoft Intune admin center.\nHotpatch updates for Windows 11 Enterprise, version 24H2 for x64 (AMD/Intel) CPU devices are now available. With hotpatch updates, you can deploy and apply security updates faster to help protect your organization from cyberattacks, while minimizing user disruptions.\nFrom the Microsoft Intune admin center, navigate to Devices > Windows updates > Create Windows quality update policy and toggle it to Allow.\nEnroll and prepare\nThe Windows quality update policy can auto-detect if your targeted devices are eligible for hotpatch updates. Devices running Windows 10 and Windows 11, version 23H2 and lower will continue to receive the standard monthly security updates, helping ensure that your ecosystem stays protected and productive.\nMaintain robust security with hotpatch updates\nThe general availability of hotpatch technology for Windows clients marks a significant step forward in enhancing security and productivity for Windows 11 Enterprise users.\nHotpatch updates help ensure that devices are secured more quickly and that users stay productive with minimal disruptions. We encourage organizations to take advantage of this new feature to maintain a robust security posture while minimizing the impact on the user experience. Hotpatch updates are generally available on Intel and AMD-powered devices as of April 2, 2025, with the feature becoming available on Arm64 devices at a later date.\nFor more information, see:\nHotpatch for Windows client now available - Windows IT Pro Blog\nHotpatch updates\nHotpatch for client comes to Windows 11 Enterprise\nSkilling: Hotpatch on Windows client and server\nThe hottest way to update Windows 11 and Windows Server 2025\nHotpatch release notes\nThe Microsoft Tunnel readiness tool now includes a check for the auditd package for Linux System Auditing (LSA). The presence of auditd is optional and not a required prerequisite by Microsoft Tunnel for the Linux server.\nWhen the mst-readiness tool runs, it now raises a non-blocking warning if the audit package isn't installed. By default, Red Hat Enterprise Linux versions 7 and later install this package by default. Ubuntu versions of Linux currently require this optional package to be installed.\nFor more information on auditd and how to install it on your Microsoft Tunnel server, see Linux system auditing.\nEndpoint Protection Manager (EPM) now supports managing file elevations on devices that run on ARM 64-bit architecture.\nApplies to:\nThe Settings Catalog lists all the settings you can configure in a device policy, and all in one place. For more information about configuring Settings Catalog profiles in Intune, see Create a policy using settings catalog.\nThere are new settings in the Settings Catalog. To see these settings, in the Microsoft Intune admin center, go to Devices > Manage devices > Configuration > Create > New policy > iOS/iPadOS or macOS for platform > Settings catalog for profile type.\nRestrictions:\nRemote Desktop:\nRestrictions:\nIntune policies for Windows Local Administrator Password Solution (LAPS) now include several new settings and updates to two previously available settings. Use of LAPS which is a Windows built-in solution can help you secure the built-in local administrator account that is present on each Windows device. All the settings that you can manage through Intune LAPS policy are described in the Windows LAPS CSP.\nThe following new settings are available: (Each setting name is a link that opens the CSP documentation for that setting.)\nThe following settings have new options available:\nBy default, each setting in LAPS policies is set to Not configured, which means the addition of these new settings won't change the behavior of your existing policies. To make use of the new settings and options, you can create new profiles or edit your existing profiles.\nApplies to:\nAs part of the Settings Catalog, you can now configure devices to automatically update to the latest OS version using DDM. To use these new settings in the Microsoft Intune admin center, go to Devices > Manage devices > Configuration > Create > New policy > iOS/iPadOS or macOSfor platform > Settings catalog for profile type.\nDeclarative device management > Software Update Enforce Latest.\nLearn more about configuring managed updates through DDM at Managed software updates.\nApplies To:\nRemote Help now provides support for multi-session AVD with several users on a single virtual machine. Earlier, Remote Help was supporting Azure Virtual Desktop (AVD) sessions with one user on one virtual machine (VM).\nFor more information, see:\nYou can now use Copilot to generate a KQL query to help you get data from across multiple devices in Intune. This capability is available in the Microsoft Intune admin center by selecting Devices > Device query > Query with Copilot. For more information, see Query with Copilot in device query.\nThe following protected apps are now available for Microsoft Intune:\nFor more information about protected apps, see Microsoft Intune protected apps.\nWe are introducing a new Update Substate in Service-side data. This substate is displayed in the reports for devices that are invalid in Microsoft Entra and is known as Not supported.\nFor more information, see Use Windows Update for Business reports for Windows Updates\nThe VPP token name column, available in the Apps workload, allows you to quickly determine the token and app association. This column is now available in the All apps list (Apps > All apps) and the app selection pane for App configuration policies (Apps > App configuration policies). For more information about VPP apps, see Manage volume-purchased apps and books with Microsoft Intune.\nApplies to:\nThe Settings Catalog lists all the settings you can configure in a device policy, and all in one place.\nThere are new settings in the Settings Catalog for Windows. To see these settings, in the Microsoft Intune admin center, go to Devices > Manage devices > Configuration > Create > New policy > Windows 10 and later > Settings catalog for profile type.\nThe new settings are:\nApplies to:\nWe've updated the Intune Connector for Active Directory to use a low privileged account to increase the security of your environment. The old connector will continue to work until deprecation in late May 2025.\nFor more information, see Deploy Microsoft Entra hybrid joined devices by using Intune and Windows Autopilot.\nManaged Home Screen for Android devices natively supports QR Code Authentication in Microsoft Entra ID. Authentication involves both a QR code and PIN. This capability eliminates the need for users to enter and re-enter long UPNs and alphanumeric passwords. For more information, see Sign in to Microsoft Teams or Managed Home Screen (MHS) with QR code.\nApplies to:\nAndroid OS version, Security patch, and Last device reboot time details are now available from the Device Information page of the Managed Home Screen app. For related information, see Configure the Microsoft Managed Home Screen app for Android Enterprise.\nApplies to:\nIn Intune, you can choose to expose a setting in the Managed Home Screen app to allow users to select a ringtone. For more information, see Configure the Microsoft Managed Home Screen app for Android Enterprise.\nApplies to:\nYou can now use Intune to manage the configuration of the Microsoft Defender CSP for DeviceControlEnabled for Device Control. DeviceControlEnabled is used to enable or disable support for the Microsoft Defender Device Control feature on Windows devices.\nYou can use the following two Microsoft Intune options to configure DeviceControlEnabled. With both options, the setting appears as Device Control Enabled, and is found in the Defender category:\nBoth the Device Control template and Settings Catalog support the following options for Device Control Enabled:\nApplies to:\nYou can now use Intune to manage the configuration of the Microsoft Defender CSP for DefaultEnforcement for Device Control. DefaultEnforcement manages the configuration of Device Control on devices that don’t receive Device Control policies or for devices that receive and evaluate a policy for Device Control when no rules in the policy are matched.\nYou can use the following two Microsoft Intune options to configure DefaultEnforcement. With both options, the setting appears as Default Enforcement, and is found in the Defender category:\nBoth the Device Control template and Settings Catalog support the following options for Default Enforcement:\nApplies to:\nThe following protected app is now available for Microsoft Intune:\nFor more information about protected apps, see Microsoft Intune protected apps.\nThe Settings Catalog lists all the settings you can configure in a device policy, and all in one place. For more information about configuring Settings Catalog profiles in Intune, see Create a policy using settings catalog.\nThere are new settings For Apple devices in the Settings Catalog. To see these settings, in the Microsoft Intune admin center, go to Devices > Manage devices > Configuration > Create > New policy > iOS/iPadOS or macOS for platform > Settings catalog for profile type.\nManaged Settings:\nNetworking > Domains:\nRestrictions:\nNetworking > Domains:\nRestrictions:\nSystem Configuration > File Provider:\nIntune is introducing limited live chat support within the Intune admin console. Live chat isn't available for all tenants or inquiries at this time.\nYou can now deploy the Intune security baseline for Windows version 24H2 to your Windows 10 and Windows 11 devices. The new baseline version uses the unified settings platform seen in the Settings Catalog, which features an improved user interface and reporting experience, consistency and accuracy improvements with setting tattooing, and the new ability to support assignment filters for profiles.\nUse of Intune security baselines can help you maintain best-practice configurations for your Windows devices and can help you rapidly deploy configurations to your Windows devices that meet the security recommendations of the applicable security teams at Microsoft.\nAs with all baselines, the default baseline represents the recommended configurations for each setting, which you can modify to meet the requirements of your organization.\nApplies to:\nWe've added Device query for multiple devices. This feature allows you to gain comprehensive insights about your entire fleet of devices using Kusto Query Language (KQL) to query across collected inventory data for your devices.\nDevice query for multiple devices is now supported for devices running Windows 10 or later. This feature is now included as part of Advanced Analytics.\nApplies to:\nWhen your Azure Tenant is licensed for Microsoft Security Copilot, you can now use Security Copilot to help you investigate Endpoint Privilege Management (EPM) file elevation requests from within the EPM support approved work flow.\nWith this capability, while reviewing the properties of a file elevation request, you'll now find option to Analyze with Copilot. Use of this option directs Security Copilot to use the files hash in a prompt Microsoft Defender Threat Intelligence to evaluate the file potential indicators of compromise so you can then make a more informed decision to either approve or deny that file elevation request. Some of the results that are returned to your current view in the admin center include:\nEPM is available as an Intune Suite add-on-capability. To learn more about how you can currently use Copilot in Intune, see Microsoft Copilot in Intune.\nTo learn more about Microsoft Security Copilot, see, Microsoft Security Copilot.\nThe Apps area in Intune, commonly known as the Apps workload, is updated to provide a more consistent UI and improved navigation structure so you can find the information you need faster. To find the App workload in Intune, navigate to Microsoft Intune admin center and select Apps.\nThe Settings Catalog lists all the settings you can configure in a device policy, and all in one place.\nThere are new settings in the Settings Catalog to Configure Multiple Display Mode for\nWindows 24H2. To see available settings, in the Microsoft Intune admin center, go to Devices > Manage devices > Configuration > Create > New policy > Windows 10 and later for platform > Settings catalog for profile type.\nThe Configure Multiple Display Mode setting allows monitors to extend or clone the display by default, facilitating the need for manual setup. It streamlines the multi-monitor configuration process, ensuring a consistent and user-friendly experience.\nApplies to:\nYou can now deploy the Intune security baseline for Microsoft Edge version 128. This update brings support for recent settings so you can continue to maintain best-practice configurations for Microsoft Edge.\nView the default configuration of settings in the updated baseline.\nFor information about security baselines with Intune, see Use security baselines to configure Windows devices in Intune.\nApplies to:\nThe following protected app is now available for Microsoft Intune:\nFor more information about protected apps, see Microsoft Intune protected apps.\nYou can now deploy two distinct instances of the security baseline for HoloLens 2. These baselines represent Microsoft’s best practice guidelines and experience from deploying and supporting HoloLens 2 devices to customers across various industries. The two baselines instances:\nStandard Security Baseline for HoloLens 2:\nThe standard security baseline for HoloLens 2 represents the recommendations for configuring security settings that are applicable to all types of customers irrespective of HoloLens 2 use case scenarios. View the default configuration of settings in the standard security baseline.\nAdvanced Security Baseline for HoloLens 2:\nThe advanced security baseline for HoloLens 2 represents the recommendations for configuring security settings for the customers who have strict security controls of their environment and require stringent security policies to be applied to any device used in their environment. View the default configuration of settings in the advanced security baseline.\nTo learn more about security baselines with Intune, see Use security baselines to configure Windows devices in Intune.\nApplies to:\nSupport Assistant is now available in Intune. It leverages AI to enhance your help and support experience, ensuring more efficient issue resolution. Support Assistant is available in the Microsoft Intune admin center by selecting Troubleshoot + support > Help and Support, or by selecting the question mark near your profile pic. Currently, the Support Assistant is in preview. You can enable and disable Support Assistant by choosing to opt in and opt out at any time. For related information, see How to get support in the Microsoft Intune admin center.\nAs of December 31, 2024, Microsoft Intune no longer supports Android device administrator management on devices with access to Google Mobile Services (GMS). This change comes after Google deprecated Android device administrator management and ceased support. Intune support and help documentation remains for devices without access to GMS running Android 15 or earlier, and Microsoft Teams devices migrating to Android Open Source Project (AOSP) management. For more information about how this change impacts your tenant, see Intune ending support for Android device administrator on devices with GMS access in December 2024.\nYou can now create up to 25 policies that customize the Company Portal and Intune app experience. The previous maximum number of Customization policies was 10. Navigate to the Intune admin center, and select Tenant administration > Customization.\nFor more information about customizing the Company Portal and Intune apps, see Customizing the user experience.\nYou can now manage the Microsoft Defender for Endpoint CSP setting for tamper protection on unenrolled devices you manage as part of the Defender for Endpoint security settings management scenario.\nWith this support, tamper protection configurations from Windows Security Experience profiles for Antivirus policies now apply to all devices instead of only to those that are enrolled with Intune.\nCustomers cannot create new Administrative Templates configuration profile through Devices > Configuration > Create > New policy > Windows 10 and later > Administrative Templates. A (retired) tag is seen next to Administrative Templates and the Create button is now greyed out. Other templates continue to be supported.\nHowever, customers can now use the Settings Catalog for creating new Administrative Templates configuration profile by navigating to Devices > Configuration > Create > New policy > Windows 10 and later > Settings Catalog.\nThere are no changes in the following UI experiences:\nFor more information, see Use ADMX templates on Windows 10/11 devices in Microsoft Intune.\nApplies to:\nIntune Wi-Fi configuration profiles for Android Enterprise personally-owned work profile devices now support configuration of pre-shared keys and proxy settings.\nYou can find these settings in the admin console in Devices > Manage devices > Configuration > Create > New Policy. Set Platform to Android Enterprise and then in the Personally-Owned Work Profile section, select Wi-Fi and then select the Create button.\nIn the Configuration settings tab, when you select Basic Wi-Fi type, several new options are available:\nSecurity type, with options for Open (no authentication), WEP-Pre-shared key, and WPA-Pre-shared key.\nProxy settings, with the option to select Automatic and then specify the proxy server URL.\nIt was possible to configure these in the past with Custom Configuration policies, but going forward, we recommend setting these in the Wi-Fi Configuration profile, because Intune is ending support for Custom policies in April 2024..\nFor more information, see Wi-Fi settings for personally-owned work profile devices..\nApplies to:\nWe're now supporting device management for Ubuntu 24.04 LTS. You can enroll and manage Linux devices running Ubuntu 24.04, and assign standard compliance policies, custom configuration scripts, and compliance scripts.\nFor more information, see the following in Intune documentation:\nApplies to:\nAt Apple WWDC 2024, Apple ended support for profile-based Apple user enrollment. For more information, see Support has ended for profile-based user enrollment with Company Portal. As a result of this change, we updated the behavior that occurs when you select Determine based on user choice as the enrollment profile type for bring-your-own-device (BYOD) enrollments.\nNow when users select I own this device during a BYOD enrollment, Microsoft Intune enrolls them via account-driven user enrollment, rather than profile-based user enrollment, and then secures only work-related apps. Less than one percent of Apple devices across all Intune tenants are currently enrolled this way, so this change doesn't affect most enrolled devices. There is no change for iOS users who select My company owns this device during a BYOD enrollment. Intune enrolls them via device enrollment with Intune Company Portal, and then secures their entire device.\nIf you currently allow users in BYOD scenarios to determine their enrollment profile type, you must take action to ensure account-driven user enrollment works by completing all prerequisites. For more information, see Set up account driven Apple user enrollment. If you don't give users the option to choose their enrollment profile type, there are no action items.\nDevice inventory lets you collect and view additional hardware properties from your managed devices to help you better understand the state of your devices and make business decisions.\nYou can now choose what you want to collect from your devices, using the catalog of properties and then view the collected properties in the Resource Explorer view.\nFor more information, see:\nApplies to:\nFor previous months, see the What's new archive.\nThese notices provide important information that can help you prepare for future Intune changes and features.\nLater in calendar year 2025, we expect iOS 26 and iPadOS 26 to be released by Apple. Microsoft Intune, including the Intune Company Portal and Intune app protection policies (APP, also known as MAM), requires iOS 17/iPadOS 17 and higher shortly after the iOS/iPadOS 26 release.\nIf you're managing iOS/iPadOS devices, you might have devices that won't be able to upgrade to the minimum supported version (iOS 17/iPadOS 17).\nGiven that Microsoft 365 mobile apps are supported on iOS 17/iPadOS 17 and higher, this change may not affect you. You likely already upgraded your OS or devices.\nTo check which devices support iOS 17 or iPadOS 17 (if applicable), see the following Apple documentation:\nNote\nUserless iOS and iPadOS devices enrolled through Automated Device Enrollment (ADE) have a slightly nuanced support statement due to their shared usage. The minimum supported OS version changes to iOS 17/iPadOS 17 while the allowed OS version changes to iOS 14/iPadOS 14 and later. See this statement about ADE Userless support for more information.\nCheck your Intune reporting to see what devices or users might be affected. For devices with mobile device management (MDM), go to Devices > All devices and filter by OS. For devices with app protection policies, go to Apps > Monitor > App protection status and use the Platform and Platform version columns to filter.\nTo manage the supported OS version in your organization, you can use Microsoft Intune controls for both MDM and APP. For more information, refer to Manage operating system versions with Intune.\nLater in calendar year 2025, we expect macOS Tahoe 26 to be released by Apple. Microsoft Intune, the Company Portal app, and the Intune mobile device management agent support macOS 14 and later. Since the Company Portal app for iOS and macOS are a unified app, this change will occur shortly after the release of macOS 26. This doesn't affect existing enrolled devices.\nThis change only affects you if you currently manage, or plan to manage, macOS devices with Intune. This change might not affect you because your users have likely already upgraded their macOS devices. For a list of supported devices, refer to macOS Sonoma is compatible with these computers.\nNote\nDevices that are currently enrolled on macOS 13.x or below will continue to remain enrolled even when those versions are no longer supported. New devices are unable to enroll if they're running macOS 13.x or below.\nCheck your Intune reporting to see what devices or users might be affected. Go to Devices > All devices and filter by macOS. You can add more columns to help identify who in your organization has devices running macOS 13.x or earlier. Ask your users to upgrade their devices to a supported OS version.\nGoogle recently updated the definition of \"Strong Integrity\" for devices running Android 13 or above, requiring hardware-backed security signals and recent security updates. For more information refer to the Android Developers Blog: Making the Play Integrity API faster, more resilient, and more private. Microsoft Intune will enforce this change by September 30, 2025. Until then we've adjusted app protection policy and compliance policy behavior to align with Google’s recommended backward compatibility guidance to minimize disruption as detailed in Improved verdicts in Android 13 and later devices | Google Play | Android Developers.\nIf you have targeted users with app protection policies and/or compliance policies that are using devices running Android 13 or above without a security update in the past 12 months, these devices will no longer meet the \"Strong Integrity\" standard.\nUser Impact: For users running devices on Android 13 or above after this change:\nNote that devices running Android versions 12 or below aren't affected by this change.\nBefore September 30, 2025, review and update your policies as needed. Ensure users with devices running Android 13 or above are receiving timely security updates. You can use the app protection status report to monitor the date of the last Android Security Patch received by the device and notify users to update as needed. The following admin options are available to help warn or block users:\nAs part of Microsoft’s Secure Future Initiative, we recently released an update to the Intune Connector for Active Directory to use a Managed Service Account instead of a local SYSTEM account for deploying Microsoft Entra hybrid joined devices with Windows Autopilot. The new connector aims to enhance security by reducing unnecessary privileges and permissions associated with the local SYSTEM account.\nImportant\nAt the end of June 2025, we'll remove the old connector which uses the local SYSTEM account. At that point, we will stop accepting enrollments from the old connector. For more details, refer to the blog: Microsoft Intune Connector for Active Directory security update\nIf you have Microsoft Entra hybrid joined devices using Windows Autopilot, you need to transition to the new connector to continue deploying and managing devices effectively. If you don't update to the new connector, you won't be able to enroll new devices using the old connector.\nUpdate your environment to the new connector by following these steps:\nFor more detailed instructions, review: Microsoft Intune Connector for Active Directory security update and Deploy Microsoft Entra hybrid joined devices by using Intune and Windows Autopilot.\nIntune has updated the required CDN endpoints for Windows, Mac and Android Open Source Project (AOSP). If you have configured your firewall to allow *.manage.microsoft.com then no action is required, otherwise, we recommend you review and update your proxy settings by April 30, 2025.\nIf you're using Intune to deploy applications and scripts, you'll need to grant access to the updated endpoints for your location. If access to the new endpoints is not granted, users won't be able to install applications or scripts, and certain functionalities may fail. The CDN endpoints are used in the following scenarios:\nUpdate your firewall rules to include the new CDN endpoints. For the best experience, we recommend using the *.manage.microsoft.com domain. If your proxy or firewall doesn't allow you to create a firewall rule using a domain, update the address as listed:\nToday, the Apple AI features for Genmojis, Writing tools, and screen capture are blocked when the app protection policy (APP) \"Send Org data to other apps\" setting is configured to a value other than \"All apps\". For more details on the current configuration, app requirements, and the list of current Apple AI controls review the blog: Microsoft Intune support for Apple Intelligence\nIn an upcoming release, Intune app protection policies have new standalone settings for blocking screen capture, Genmojis, and Writing tools. These standalone settings are supported by apps that have updated to version 19.7.12 or later for Xcode 15 and 20.4.0 or later for Xcode 16 of the Intune App SDK and App Wrapping Tool.\nIf you configured the APP \"Send Org data to other apps\" setting to a value other than \"All apps\", then the new \"Genmoji\", \"Writing Tools\" and \"Screen capture\" settings are set to Block in your app protection policy to prevent changes to your current user experience.\nNote\nIf you configured an app configuration policy (ACP) to allow for screen capture, it overrides the APP setting. We recommend updating the new APP setting to Allow and removing the ACP setting. For more information about the screen capture control, review iOS/iPadOS app protection policy settings | Microsoft Learn.\nReview and update your app protection policies if you'd like more granular controls for blocking or allowing specific AI features. (Apps > Protection > select a policy > Properties > Basics > Apps > Data protection)\nIn an upcoming version (20.3.0) of the Intune App SDK and Intune App Wrapping Tool for iOS, support is added to alert users when a screen capture action (including recording and mirroring) is detected in a managed app. The alert is only visible to users if you have configured an app protection policy (APP) to block screen capture.\nIf APP has been configured to block screen capturing, users see an alert indicating that screen capture actions are blocked by their organization when they attempt to screenshot, screen record, or screen mirror.\nFor apps that have updated to the latest Intune App SDK or Intune App Wrapping Tool versions, screen capture is blocked if you configured \"Send Org data to other apps\" to a value other than \"All apps\". To allow screen capture for your iOS/iPadOS devices, configure the Managed apps app configuration policy setting \"com.microsoft.intune.mam.screencapturecontrol\" to Disabled.\nUpdate your IT admin documentation and notify your helpdesk or users as needed. You can learn more about blocking screen capture in the blog: New block screen capture for iOS/iPadOS MAM protected apps\nIn late May 2025 (previously March), a select number of old Microsoft Graph Beta API windowsAutopilotDeploymentProfile properties used for Windows Autopilot self-deploying mode and pre-provisioning are removed and stop working. The same data can be found using newer Graph API properties.\nIf you have automation or scripts using the following Windows Autopilot properties, you must update to the new properties to prevent them from breaking.\nUpdate your automation or scripts to use the new Graph API properties to avoid deployment issues.\nAdditional information:\nWe recently released updated versions of the Intune App SDK and the Intune App Wrapping Tool. Included in these releases (v19.7.5+ for Xcode 15 and v20.2.0+ for Xcode 16) is the support for blocking screen capture, Genmojis, and writing tools in response to the new AI features in iOS/iPadOS 18.2.\nFor apps that have updated to the latest Intune App SDK or Intune App Wrapping Tool versions screen capture will be blocked if you configured \"Send Org data to other apps\" to a value other than \"All apps\". To allow screen capture for your iOS/iPadOS devices, configure the Managed apps app configuration policy setting \"com.microsoft.intune.mam.screencapturecontrol\" to Disabled.\nReview your app protection policies and if needed, create a Managed apps app configuration policy to allow screen capture by configuring the above setting (Apps > App configuration policies > Create > Managed apps > Step 3 ‘Settings’ under General configuration). For more information review, iOS app protection policy settings – Data protection and App configuration policies - Managed apps.\nTo support the upcoming release of iOS/iPadOS 18.2, update to the latest versions of the Intune App SDK and the Intune App Wrapping Tool to ensure applications stay secure and run smoothly. Important: If you don't update to the latest versions, some app protection policies may not apply to your app in certain scenarios. Review the following GitHub announcements for more details on the specific impact:\nAs a best practice, always update your iOS apps to the latest App SDK or App Wrapping Tool to ensure that your app continues to run smoothly.\nIf you have applications using the Intune App SDK or Intune App Wrapping Tool, you'll need to update to the latest version to support iOS 18.2.\nFor apps running on iOS 18.2, you must update to the new version of the Intune App SDK for iOS:\nFor apps running on iOS 18.2, you must update to the new version of the Intune App Wrapping Tool for iOS:\nImportant\nThe listed SDK releases support blocking screen capture, Genmojis, and writing tools in response to new AI features in iOS 18.2. For apps that have updated to these SDK versions, screen capture block is applied if you have configured Send Org data to other apps to a value other than All apps. See iOS/iPadOS app protection policy settings for more info. You can configure app configuration policy setting com.microsoft.intune.mam.screencapturecontrol = Disabled if you wish to allow screen capture for your iOS devices. See App configuration policies for Microsoft Intune for more info. Intune will be providing more granular controls for blocking specific AI features in the future. Follow What's new in Microsoft Intune to stay up to date.\nNotify your users as applicable, to ensure they upgrade their apps to the latest version prior to upgrading to iOS 18.2. You can review the Intune App SDK version in use by your users in the Microsoft Intune admin center by navigating to Apps > Monitor > App protection status, then review Platform version and iOS SDK version.\nIf you have questions, leave a comment on the applicable GitHub announcement. Additionally, if you haven't already, navigate to the applicable GitHub repository and subscribe to Releases and Discussions (Watch > Custom > select Releases, Discussions) to ensure you stay up-to-date with the latest SDK releases, updates, and other important announcements.\nStarting with Intune's September (2409) service release, the IntuneMAMUPN, IntuneMAMOID, and IntuneMAMDeviceID app configuration values will be automatically sent to managed applications on Intune enrolled iOS devices for the following apps: Microsoft Excel, Microsoft Outlook, Microsoft PowerPoint, Microsoft Teams and Microsoft Word. Intune will continue to expand this list to include additional managed apps.\nIf these values aren't configured correctly for iOS devices, there's a possibility of either the policy not getting delivered to the app or the wrong policy is delivered. For more information, see Support tip: Intune MAM users on iOS/iPadOS userless devices may be blocked in rare cases.\nNo additional action is needed.\nWith the May 10, 2022, Windows update (KB5014754), changes were made to the Active Directory Kerberos Key Distribution (KDC) behavior in Windows Server 2008 and later versions to mitigate elevation of privilege vulnerabilities associated with certificate spoofing. Windows enforces these changes on February 11, 2025.\nTo prepare for this change, Intune has released the ability to include the security identifier to strongly map SCEP and PKCS certificates. For more information, review the blog: Support tip: Implementing strong mapping in Microsoft Intune certificates.\nThese changes will impact SCEP and PKCS certificates delivered by Intune for Microsoft Entra hybrid joined users or devices. If a certificate can't be strongly mapped, authentication will be denied. To enable strong mapping:\nFor detailed steps and additional guidance, review the blog: Support tip: Implementing strong mapping in Microsoft Intune certificates\nIf you use SCEP or PKCS certificates for Microsoft Entra Hybrid joined users or devices, you'll need to take action before February 11, 2025 to either:\nWe've recently released new versions of the Intune App SDK and Intune App Wrapping Tool for Android to support Android 15. We recommend upgrading your app to the latest SDK or wrapper versions to ensure applications stay secure and run smoothly.\nIf you have applications using the Intune App SDK or Intune App Wrapping Tool for Android, it's recommended that you update your app to the latest version to support Android 15.\nIf you choose to build apps targeting Android API 35, you need to adopt the new version of the Intune App SDK for Android (v11.0.0). If you wrapped your app and are targeting API 35 you need to use the new version of the App wrapper (v1.0.4549.6).\nNote\nAs a reminder, while apps must update to the latest SDK if targeting Android 15, apps don't need to update the SDK to run on Android 15.\nYou should also plan to update your documentation or developer guidance if applicable to include this change in support for the SDK.\nHere are the public repositories:\nIn October 2024, Intune supports Android 10 and later for user-based management methods, which includes:\nMoving forward, we'll end support for one or two versions annually in October until we only support the latest four major versions of Android. You can learn more about this change by reading the blog: Intune moving to support Android 10 and later for user-based management methods in October 2024.\nNote\nUserless methods of Android device management (Dedicated and AOSP userless) and Microsoft Teams certified Android devices won't be impacted by this change.\nFor user-based management methods (as listed above), Android devices running Android 9 or earlier won't be supported. For devices on unsupported Android OS versions:\nWhile Intune won't prevent enrollment or management of devices on unsupported Android OS versions, functionality isn't guaranteed, and use isn't recommended.\nNotify your helpdesk, if applicable, about this updated support statement. The following admin options are available to help warn or block users:\nFor more information, review: Manage operating system versions with Microsoft Intune.\nToday, when creating iOS/iPadOS enrollment profiles, \"Device enrollment with Company Portal\" is shown as the default method. In an upcoming service release, the default method will change to \"Web based device enrollment\" during profile creation. Additionally for new tenants, if no enrollment profile is created, the user will enroll using web-based device enrollment.\nNote\nFor web enrollment, you need to deploy the single sign-on (SSO) extension policy to enable just in time (JIT) registration, for more information review: Set up just in time registration in Microsoft Intune.\nThis is an update to the user interface when creating new iOS/iPadOS enrollment profiles to display \"Web based device enrollment\" as the default method, existing profiles aren't impacted. For new tenants, if no enrollment profile is created, the user will enroll using web-based device enrollment.\nUpdate your documentation and user guidance as needed. If you currently use device enrollment with Company Portal, we recommend moving to web based device enrollment and deploying the SSO extension policy to enable JIT registration.\nAdditional information:\nGoogle has deprecated Android device administrator management, continues to remove management capabilities, and no longer provides fixes or improvements. Due to these changes, Intune will be ending support for Android device administrator management on devices with access to Google Mobile Services (GMS) beginning December 31, 2024. Until that time, we support device administrator management on devices running Android 14 and earlier. For more details, read the blog: Microsoft Intune ending support for Android device administrator on devices with GMS access.\nAfter Intune ends support for Android device administrator, devices with access to GMS will be impacted in the following ways:\nStop enrolling devices into Android device administrator and migrate impacted devices to other management methods. You can check your Intune reporting to see which devices or users might be affected. Go to Devices > All devices and filter the OS column to Android (device administrator) to see the list of devices.\nRead the blog, Microsoft Intune ending support for Android device administrator on devices with GMS access, for our recommended alternative Android device management methods and information about the impact to devices without access to GMS.\n\n\t\t\t\t\tWas this page helpful?\n\t\t\t\t", "sentiment": "positive", "sentiment_score": 1.0, "summary": "News article: What's new in Microsoft Intune | Microsoft Learn", "source": "google_search", "date": "2025-06-18T02:51:09.707652Z"}, {"headline": "403 Forbidden", "description": "", "url": "https://news.sap.com/2025/05/new-sap-business-suite-acceleration-program-microsoft-cloud/", "image_url": "", "full_content": "", "sentiment": "negative", "sentiment_score": -0.4215, "summary": "News article: 403 Forbidden", "source": "google_search", "date": "2025-06-18T02:51:10.507109Z"}, {"headline": "Microsoft Developer", "description": "Any platform. Any language. Our tools. Develop solutions, on your terms, using Microsoft products and services.", "url": "https://developer.microsoft.com/en-us/", "image_url": "https://developer.microsoft.com/_devcom/images/logo-ms-social.png", "full_content": "Survey – Help us improve the Microsoft Developer website\nDeveloper communities, tools, and resources for solving the problems of today and building the innovations of tomorrow.\nMenu\nFeatured products\nNews and updates\nLanguages\nCommunities\nHubs\nDeveloper story\nBlogs\nEvents\nLearn\nGet exclusive resources and community support for building people-centric, cross-platform productivity experiences that extend Microsoft 365.\nLearn how to build a collaborative app or extend your Microsoft 365 app.\nGet hands-on with Microsoft 365 code samples and start building applications today.\nTurn your ideas into solutions with Microsoft cloud services. Visit the Azure Developers Hub to learn how to build how you want and deploy where you want.\nLearn how to build and manage powerful applications using Microsoft Azure cloud services.\nFilter thousands of Azure code samples by language to find what you need.\nBuild any type of application. Work together in real time. Diagnose and stop problems before they happen. Use your favorite language to deliver apps and services on any platform, any device.\nLearn how to use Visual Studio Code and code faster with AI.\nFilter thousands of VS Code code samples by language to find what you need.\nBuild experiences that reach users everywhere. The Windows Dev Center can show you how.\nLearn how to design, develop, and deploy apps and solutions for Windows PCs and other devices.\nFilter thousands of samples by language to find what you need.\nJune 16, 2025\nIn this session we will go over everything you need to know from Microsoft Build 2025 and learn about the most important releases and announcement.\nDevelop intelligent agents using the tools and languages you already know. Whether you're designing visually or coding end-to-end, you have the flexibility to create, extend, and scale—your way.\nMicrosoft Azure is your platform for developing, testing, and deploying generative AI apps responsibly.\nStart using GitHub Copilot for free in Visual Studio Code.\nLearn more about how Microsoft Discovery can help scientists and engineers transform research and development.\nAccess Microsoft tools, resources, and documentation for the following languages:\nConnect with fellow developers, attend a local user group, find resources to get you started, and discover what's happening in the community.\nJoin our Azure tech groups to connect with fellow developers and stay updated on the latest advancements.\nEngage and learn from Microsoft's Cloud advocates about Microsoft Cloud + AI platform.\nMicrosoft Reactor connects you with the developers and startups that share your goals.\nEnhance your coding experience with AI-powered features in Visual Studio Code.\nConnect with millions of developers and stay updated on the latest .NET advancements.\nJoin the GitHub Community to ask questions, get answers, and share your expertise with fellow developers.\nOur mission is to empower every person and every organization on the planet to achieve more.\nFrom community to hands-on labs, learn your way and skill up for what's next in AI.\nUnlock new development opportunities and resources with APIs.\nDevEx is about helping developers write code—and do it in an optimized environment. Learn how to enhance DevEx for your organization.\nCreate bold, immersive game experiences that attract and engage players.\n“Microsoft's commitment to ethics, to compliance, and providing tools that enable that – particularly around the data and the use of data and AI – is a massive accelerator.”\n— Luke Housego, Chief Architect Helfie.ai\nGitHub Copilot\nAzure OpenAI\nDid you know that developers use an average of 16 tools per day? A typical developer works across their code editor, a terminal, several command-line tools and web portals - not to mention all the SDKs and packages they take a dependency on in their code.\nCurated plans and resources, designed to support business and technical roles, individuals, and organizations in building AI skills.\nPlan and prepare to develop AI solutions on Azure.\nCreate agentic AI solutions by using Azure AI Foundry.\nLearn to build AI Agents.\nExplore this documentation designed for experienced developers who are new to building generative AI apps on Azure using Azure OpenAI Services and their favorite programming language.\nAll training topics for Azure Microsoft Learn.\nPractical foundation for AI Agents: A developer's guide on Azure AI Foundry, apps, and data.\nBuild AI apps with Azure Services and best practices.\nDevelop a RAG-based solution with your own data using Azure AI Foundry.\nComprehensive documentation and resources you need to innovate securely and efficiently.\nElevate your Copilot and agent experience through the resources on this learning hub\nHow Microsoft Copilot can revolutionize your workflow.\nHow to use Copilot Studio actions to extend capabilities for developers.\nChallenge project - Build a declarative agent to chat with your data.\nEverything you need to learn how to build applications with GitHub Copilot Agent Mode.\nAll about DevOps practices, Git version control, Agile methods, and DevOps at Microsoft.\nExplore this article on Azure DevOps and how this service supports a collaborative culture.\nLearn to foster the DevOps values of transparency and team cooperation with an agile approach to software development.\nDiscover Azure Automation with DevOps Integration.\nCollaborate on software development through source control, work tracking, and continuous integration and delivery.\nYour jumping off point for the full breadth of technical guidance and resources for Security professionals.\nAll training topics for Security, Compliance, and Identity on Microsoft Learn.\nTake advantage of Microsoft Security Copilot.\nSecurity for AI with Microsoft Purview and Defender for Cloud.\nExplore the Zero Trust adoption framework.\nFollow us on the web", "sentiment": "positive", "sentiment_score": 0.9985, "summary": "News article: Microsoft Developer", "source": "google_search", "date": "2025-06-18T02:51:11.223168Z"}, {"headline": "Question: Sign in request from Microsoft 365 daily when working in Excel Online", "description": "Hello there, \n\n  \nWhen my colleagues and I work in Firefox, we get this message daily, “Your browser settings are preventing an optimal experience with Microsoft 365. Allow access to improve your expe...", "url": "https://reddit.com/r/firefox/comments/1led0la/question_sign_in_request_from_microsoft_365_daily/", "image_url": "", "full_content": "Hello there, \n\n  \nWhen my colleagues and I work in Firefox, we get this message daily, “Your browser settings are preventing an optimal experience with Microsoft 365. Allow access to improve your experience”. How can we fix this? The privacy settings are normal, out-of-the-box. It's fine to click the button once a day, but more colleagues getting annoyed by it.  \n\n  \n<PERSON>\n\nhttps://preview.redd.it/5p8lzfaeqn7f1.png?width=585&format=png&auto=webp&s=402d2f75838d74e51e55cf9f8a9642601eaf65eb", "sentiment": "positive", "sentiment_score": 0.265, "summary": "Reddit post: Question: Sign in request from Microsoft 365 daily when working in Excel Online", "source": "reddit", "date": "2025-06-18 02:48:15 UTC"}, {"headline": "2x Free Bedrock Party Frog Codes", "description": "Hey everyone, I got these for free in Japan and have no use for them as I play on Java. If you’d like one, please comment and the first 2 ppl to comment I will message you a code! I saw these listed f...", "url": "https://reddit.com/r/Minecraft/comments/1lecyrv/2x_free_bedrock_party_frog_codes/", "image_url": "", "full_content": "Hey everyone, I got these for free in Japan and have no use for them as I play on Java. If you’d like one, please comment and the first 2 ppl to comment I will message you a code! I saw these listed for like $50usd on eBay but screw that, I’d prefer it go to someone who wants it for free, since I got them for free!", "sentiment": "positive", "sentiment_score": 0.9537, "summary": "Reddit post: 2x Free Bedrock Party Frog Codes", "source": "reddit", "date": "2025-06-18 02:45:01 UTC"}, {"headline": "mcp-server-time not starting, what is the fix?", "description": "MCP-server-time is not starting in VS Code. I am using WSL in Windows 11\n\nGetting this error:\n\n```\n\n2025-06-18 10:28:22.936 [info] Connection state: Error Process exited with code 9009\n2025-06-18 10:2...", "url": "https://reddit.com/r/mcp/comments/1lecx3o/mcpservertime_not_starting_what_is_the_fix/", "image_url": "", "full_content": "MCP-server-time is not starting in VS Code. I am using WSL in Windows 11\n\nGetting this error:\n\n```\n\n2025-06-18 10:28:22.936 [info] Connection state: Error Process exited with code 9009\n2025-06-18 10:28:22.936 [error] Server exited before responding to `initialize` request.\n2025-06-18 10:30:55.764 [info] Stopping server mcp-server-time\n2025-06-18 10:30:55.785 [info] Starting server mcp-server-time\n2025-06-18 10:30:55.788 [info] Connection state: Starting\n2025-06-18 10:30:55.802 [info] Starting server from LocalProcess extension host\n2025-06-18 10:30:55.908 [info] Connection state: Starting\n2025-06-18 10:30:55.909 [info] Connection state: Running\n2025-06-18 10:30:56.027 [warning] [server stderr] Python was not found; run without arguments to install from the Microsoft Store, or disable this shortcut from Settings > Apps > Advanced app settings > App execution aliases.\n2025-06-18 10:30:56.036 [info] Connection state: Error Process exited with code 9009\n2025-06-18 10:30:56.037 [error] Server exited before responding to `initialize` request.\n\n```\n\nThis is my ```settings.json``` file.\n\n```\n    \"mcp\": {\n        \"inputs\": [],\n        \"servers\": {\n            \"mcp-server-time\": {\n                \"command\": \"python\",\n                \"args\": [\n                    \"-m\",\n                    \"mcp_server_time\",\n                    \"--local-timezone=America/Los_Angeles\"\n                ],\n                \"env\": {}\n            }\n        }\n    }\n\n```", "sentiment": "negative", "sentiment_score": -0.913, "summary": "Reddit post: mcp-server-time not starting, what is the fix?", "source": "reddit", "date": "2025-06-18 02:41:51 UTC"}, {"headline": "mcp-server-time not starting", "description": "MCP-server-time is not starting in VS Code. I am using WSL in Windows 11\n\nGetting this error: \n\n```\n2025-06-18 10:28:22.936 [info] Connection state: Error Process exited with code 9009\n2025-06-18 10:2...", "url": "https://reddit.com/r/vscode/comments/1lecwiy/mcpservertime_not_starting/", "image_url": "", "full_content": "MCP-server-time is not starting in VS Code. I am using WSL in Windows 11\n\nGetting this error: \n\n```\n2025-06-18 10:28:22.936 [info] Connection state: Error Process exited with code 9009\n2025-06-18 10:28:22.936 [error] Server exited before responding to `initialize` request.\n2025-06-18 10:30:55.764 [info] Stopping server mcp-server-time\n2025-06-18 10:30:55.785 [info] Starting server mcp-server-time\n2025-06-18 10:30:55.788 [info] Connection state: Starting\n2025-06-18 10:30:55.802 [info] Starting server from LocalProcess extension host\n2025-06-18 10:30:55.908 [info] Connection state: Starting\n2025-06-18 10:30:55.909 [info] Connection state: Running\n2025-06-18 10:30:56.027 [warning] [server stderr] Python was not found; run without arguments to install from the Microsoft Store, or disable this shortcut from Settings > Apps > Advanced app settings > App execution aliases.\n2025-06-18 10:30:56.036 [info] Connection state: Error Process exited with code 9009\n2025-06-18 10:30:56.037 [error] Server exited before responding to `initialize` request.\n```\n\nThis is my ```settings.json``` file.\n\n```\n    \"mcp\": {\n        \"inputs\": [],\n        \"servers\": {\n            \"mcp-server-time\": {\n                \"command\": \"python\",\n                \"args\": [\n                    \"-m\",\n                    \"mcp_server_time\",\n                    \"--local-timezone=America/Los_Angeles\"\n                ],\n                \"env\": {}\n            }\n        }\n    }\n```", "sentiment": "negative", "sentiment_score": -0.913, "summary": "Reddit post: mcp-server-time not starting", "source": "reddit", "date": "2025-06-18 02:40:55 UTC"}, {"headline": "The Early Evolution of Sound and Music in Video Games: 1986", "description": "*Note that this is a shorter, chronological version covering one year at a time (1985-1991). See* [*my website*](https://minirevver.weebly.com/the-early-evolution-of-sound-and-music-in-video-games.htm...", "url": "https://reddit.com/r/retrogaming/comments/1lecqyn/the_early_evolution_of_sound_and_music_in_video/", "image_url": "", "full_content": "*Note that this is a shorter, chronological version covering one year at a time (1985-1991). See* [*my website*](https://minirevver.weebly.com/the-early-evolution-of-sound-and-music-in-video-games.html) *for the complete essay spanning 60+ pages and covering each sound chip in detail.*\n\nhttps://preview.redd.it/jg3punyvkn7f1.png?width=1080&format=png&auto=webp&s=3109fe56acf6f2fbee13e4e9edff55163118a9bb\n\nLet’s start this year off with a few standout C64 OSTs. <PERSON> crafted an influential OST this year in ​[Comic Bakery (C64)](https://www.youtube.com/watch?v=nf29ShkoAiA), later remixed in <PERSON>’s Jurassic Park themes for the [NES](https://www.youtube.com/watch?v=e9tU1i-b6Bs) and [GB](https://www.youtube.com/watch?v=ywRGNGRmRws) (1993). The main attraction here is the title theme (the loader tune was used in several other games so I don't count it as part of this particular OST), an electro/proto-techno track that makes it pretty easy to forget that there's just three channels and no drums when it's all playing. The chorused bass (PWM - pulse-width modulation) and arpeggiated chords in combination with a strong melodic hook became iconic for C64 chiptunes in particular, and aspects like the rhythm of the chords and how the bridge and breakdown are done have possibly even inspired some non-game electronic music that came much later. The in-game tracks are more old fashioned, mostly lacking percussion despite there being no SFX and with somewhat annoying lead instruments at times, though there are a few neat moments where they veer off into zany, cartoon-ish segments.\n\nThat same year, Galway also composed for ​​[Rambo: First Blood Part II (C64)](https://www.youtube.com/watch?v=NHQFbyOzwBc). This OST has an epic, well developed main theme (loader) spanning several minutes, a varied spectrum of moods and starting in a unique way with the morse code-style beeps shifting into a melody - it's quite an achievement in itself at the time. That is then followed by a cool ambient-like piece (very rare at the time), developing into a varied slide-heavy solo employing arpeggio blasts in key spots for emphasis and building up some appropriate expectations before the game starts. In-game we're treated to a thrilling track first referencing the movie theme, then developing it into a catchy ‘80s electro hook. It's over two minutes in length, taking interesting turns in its progression and eventually climaxing in a howling solo in its second half. The only thing missing from this OST is percussion.\n\nhttps://preview.redd.it/9nlwwc9xkn7f1.png?width=1515&format=png&auto=webp&s=67c0b4a5c4af38a947fee0e1b0c48eda0e3bb60d\n\n[International Karate](https://www.youtube.com/watch?v=dvmSpZWW45k) by Rob Hubbard is another C64 standout from this year. It’s (mainly) an electro ballad which remixes the theme from Merry Christmas Mr Lawrence, and makes good use of fast arpeggios, chunky tom sounds, and ring modulation for glitchy SFX/percussion. It also features two lengthy modulated solos with some tight slide work, and an unexpected tempo switch-up into a funk segment about 4 minutes into the piece. My one point of criticism is that parts of the tracker are rather noisy and high pitched, which can get a bit tiresome.\n\nThe aforementioned composers’ work, the C64's long life and low cost (after its first couple of years), and earlier on its long load times when booting up a game, all led to a vibrant homebrew scene (demoscene) in Europe. There, audiovisual demos became almost as important as the games themselves, sometimes debuting new tricks before they were seen in games (see for example [Swinth/Light Fantastic](https://www.youtube.com/watch?v=AiHyTJsE3AU&list=PLFTPD_uXbdo-Qg5G3zia30LbuXIGNVt1N) from 1985). The load time seems not significantly affected by playing music on the SID chip if custom loader code was used, meaning composers had their [time to shine](https://www.youtube.com/watch?v=6b-ANpXEPBk) here!\n\nhttps://preview.redd.it/fvysoft2ln7f1.png?width=1100&format=png&auto=webp&s=90ac221f28d334368ec29c72a445f926fa9a50a9\n\nNew to the NES’ **RP2A03** chip was a fairly underutilized [DPCM](https://en.wikipedia.org/wiki/Differential_pulse-code_modulation) channel for crunchy, lo-fi sample playback (usually percussion, though decent voice samples were possible as heard in [Big Bird's Hide and Speak](https://www.youtube.com/watch?v=Ut1amzhaZkE&t=31s) (1990) for example).\n\n[Gumshoe](https://www.youtube.com/watch?v=1vjeHJuFAjI) (1986) by Tanaka is probably the first example of samples used for music in a NES game, and they can be heard in the Stage 1 intro percussion. While this sampled percussion debut isn't the best example on the NES, samples would do wonders for various later soundtracks on the system such as [Castlevania II](https://www.youtube.com/watch?v=s7t5jAsTcuY&list=PL9F796EFB79C2BA4F), [Famicom Wars](https://www.youtube.com/watch?v=zhfW0kzrj5c&list=PL859DE9F0E27106FD&index=10), [SMB 2](https://www.youtube.com/watch?v=LnttCOuRMVQ&list=PL04F6446580894A57&index=4) (Western ver.) & [SMB 3](https://www.youtube.com/watch?v=Qd78OMGLkVA&list=PL6B16BD8C36B02A5D), and more. For stage 2, Tanaka also combines the triangle and noise channels for beefier snares, in a very similar way to what was more famously and effectively used by Sunsoft, [Tim Follin](https://www.vgmpf.com/Wiki/index.php?title=Tim_Follin) and [Jeroen Tel](https://www.vgmpf.com/Wiki/index.php/Jeroen_Tel) later on.\n\nBy 1986, other talented musicians had also emerged to compose for the NES, such as the ones working for Konami (The Goonies, King Kong 2), Tecmo (Solomon’s Key), Capcom (Ghosts ‘n Goblins) and Enix (Dragon Quest). Out of these, Konami’s OSTs are easily the best sounding, with a more balanced and impactful low end.\n\nhttps://preview.redd.it/urqjzt19ln7f1.png?width=795&format=png&auto=webp&s=7d6bf176b769d5febea477beaa20a968134956c1\n\nLet’s have another look at the powerful [FM synthesis](https://www.youtube.com/watch?v=vvBl3YUBUyY)\\-based YM2151/OPM chip, used in various arcade machines, but also in the Japanese X1 and X68000 computers. Since it lacked built-in PCM playback capabilities, arcade game developers would generally combine it with a separate PCM chip. The more advanced of these chips, combined with the already capable YM2151, made for a very powerful setup akin to the Neo Geo’s YM2610, before its debut.\n\nSega was among the first adopters of FM synth, and also among the best users of it, although there is a pretty wide range in quality. [Out Run/OutRun (ARC, YM2151 w/ SegaPCM)](https://www.youtube.com/watch?v=aEz1BVeGO2o) and [Fantasy Zone (ARC)](https://www.youtube.com/watch?v=PDIqQWt7OI8&list=PLqpgr9DZ71Lq8kOvek4S9ZYj1_GBDmk9o&index=2)(pure FM), both by [Hiroshi Kawaguchi](https://www.vgmpf.com/Wiki/index.php?title=Hiroshi_Kawaguchi), distinguished themselves with a latin/caribbean hybrid sound mixed with some jazz fusion, R&B and funk, most of which not particularly common for games or their genres at the time. Jazz fusion, or the JP variant of it called City Pop there, was already a phenomenon in Japan, but western music was mostly dominated by rock, synth pop (though this often included elements of funk and fusion), new wave and metal at the time. Overall, Sega's OSTs tended to sound energetic, punchy and relatively raw, with some more laid back and smooth takes like the ending theme from OutRun, or the one from the later [Super Monaco GP](https://www.youtube.com/watch?v=oQCtEQO08g4&list=PLP8_sGncyT5QJvTsUE59HKCyy4eR07sJQ&index=7).\n\nhttps://preview.redd.it/nv9okqqbln7f1.png?width=850&format=png&auto=webp&s=2c44c167fe5d8598aed2a8b90e8d5d55bcdac09e\n\nThe YM3812/OPL2 sound chip, probably best known via the [AdLib sound card](https://www.youtube.com/watch?v=iAt_OKfjS3E) for DOS PCs in the west, is a relatively low cost FM synthesis chip that was first used in arcade games this year ([Rygar](https://www.youtube.com/watch?v=-NDBm0j-qIM&list=PLTEEa2-9mZ7aU9JLkVKAYSgPYDj9cLq8p&index=2), ARC). Its prequel, the YM3526/OPL, is quite similar but was used only in a few games between 1985-1988. These 2-operator (for reference, the Mega Drive's FM is 4-op) chips are known for their chunky bass, '80s synth sounding leads, [phaser](https://www.youtube.com/shorts/meVCIffTaXo)\\-like effects, spitty/flaky snares, and an often metallic, somewhat harsh overall sound, output in mono.\n\nWhile the OPL2 wasn’t among the best of mid-late '80s game audio hardware, it was an upgrade over the basic PSG chips by providing more timbre variety, dynamic modulation, more channels, good clarity and deeper bass than most contemporary chips in a similar price range. It was potentially a big upgrade for DOS PC players compared to the single channel beeper or SN76489-like sound that came before, and it became that platform’s standard audio solution until \\~1994.\n\n[Bubble Bobble](https://www.youtube.com/watch?v=J4EJleGc360&list=PL0258EFFACC54A264&index=2) (ARC, 1986) features pretty solid use of the first OPL chip. The octave bass and bell lead-driven pop of the main theme, which FM synth sounds like it was made for, probably influenced many FM-based OSTs like it. Of note sound design-wise is the chorus effect on the bass, as well as the low strings, which gave them a fuller sound. Still, only 7/9 channels are used and there's no percussion. \n\nhttps://preview.redd.it/81h2br2eln7f1.png?width=1235&format=png&auto=webp&s=a92605ac7cebbeeb6a99f6e0ba0ea34eb84fa9d8\n\nGoing back to the AY-3-8910, the biggest differences with it compared to the SN76489 chip are the lower tuning (more bass, less treble or \"pitchyness\"), and the editable envelope cycle time. A trick related to the latter, used by some western composers, made the chip produce modulated sawtooth or triangle wave-like bass sounds. The first game OST to do this is probably [Ghosts ‘n Goblins](https://www.youtube.com/watch?v=qPa0K5Cth40&list=PLznWdSFQxsPunF_A7OjVYsOqJgRzesZ5m&index=8) (CPC, 1986) by David Whittaker. Some other OSTs followed, such as [A Prehistoric Tale](https://www.youtube.com/watch?v=ehW1dT_cowQ&list=PLcOvOFs2mVCTiigZN-Rc9cu5l4uThnRwX) (1990), [The Adventures of Quik & Silva](https://vgmrips.net/packs/pack/the-adventures-of-quik-silva-atari-st) (1991), and [Awesome](https://www.youtube.com/watch?v=cdlj491KfKA&t=1m33s) (1991) for the Atari ST, and later on it became fairly common in the western homebrew scene. Besides those, the Spectrum's and CPC's variant of the AY-3 chip has stereo support, but so does the Game Gear version of the SN7.\n\nOn the Amiga, [Adventure Construction Set](https://www.youtube.com/watch?v=jY9bi48GUtY&t=19s) (sound and music by [David Warhol](https://www.vgmpf.com/Wiki/index.php?title=David_Warhol)) featured pretty decent chamber/orchestral music fitting for a medieval fantasy game. The sample quality isn't the best though - it sounds kind of dry and there seems to be an '80s e-piano sample in the mix. From the same year, the adventure game [The Pawn's intro track](https://www.youtube.com/watch?v=QDHBa3RB5p8) by John Molloy sounds fairly similar at first with its calm flute, harp and bell/marimba-based songs, but a bit into it introduces some alright drum samples and '80s synth slap bass - it's a bit of a transitional title between the old fashioned styles of other platforms' early games and the contemporary '80s-early '90s music that a lot of Amiga music is associated with.\n\nhttps://preview.redd.it/umq911pqmn7f1.png?width=1000&format=png&auto=webp&s=2298721419b5edd76a3fde8660f03482ba00ff8d\n\nThe Amiga could also do streaming audio recordings (sample playback from disk or a hard drive) in mono, though this was mainly used for intros and earlier on in its lifespan due to hardware limitations. In [The Halley Project](https://www.youtube.com/watch?v=_w83pABa7QA) (still 1986), the intro track by Thomas F. F. Snyder is a very early example of streaming audio, as well as for vocal tracks in games, and longer samples fused together to make up a short, recorded live song. The timing is not quite there, and the song is short and cheesy, but it does sound very nice and is a fun novelty for the time. This streaming audio method became a bit of a trend during the next year, but most post-1987 games would abandon it - probably due to technical limitations leading to very short loops of music.\n\nWhen it comes to sound effects, C64 developers fairly often skipped them to prioritize the music, or gave the option of either having SFX or music during gameplay - understandable since with only three channels, the SFX interrupting the music is generally pretty noticeable (see [Ghosts ‘n Goblins](https://www.youtube.com/watch?v=rHjUJNYD_GY&t=272s) (1986) or [The Great Giana Sisters](https://www.youtube.com/watch?v=eEpQCCA7S1g&t=80s) (1987) for example). Out of the latter, [Sanxion](https://www.youtube.com/watch?v=iNx1nN8eHhU&t=30s) (1986) is a good earlier example where the SFX work is pretty solid.\n\nAs for the YM2203 chip, although the FM channels are generally seen as the star, many developers continued to use the PSG channels for drums, chords, \"overdubs\" with the FM, melodies, or SFX - [Silpheed](https://www.youtube.com/watch?v=3IpFlW2keZ4) (1986) is a good example of the latter.\n\nOn the Amiga, [One on One](https://www.youtube.com/watch?v=4AEMFjrqfNc&t=27s) and Archon show off some solid crowd/announcer samples, and footstep samples, respectively. Defender of the Crown features decent galloping sfx for its jousting segments, though it's otherwise silent. Animal Kingdom (an encyclopedia), [Chessmaster 2000](https://www.youtube.com/watch?v=LCRxzi85Az0&t=33s) and a few others introduced decent speech synthesis for Amiga games. By that I mean it sounds like Microsoft Sam, except about 12 years earlier, so in that context it's pretty good, but to our post-AI voice generator ears it might not sound like much.\n\n\\---\n\n[Some other OSTs that defined the C64 sound and/or used its features creatively](https://www.youtube.com/watch?v=8E4nJCcG4iQ&list=PLznWdSFQxsPsFCeWQF-maCknmpUlvUJKr) (YT playlist):\n\nSanxion (1986, Loader song), Ghosts 'n Goblins (C64, 1986)(different), Miami Vice (C64, 1986), The Sacred Armour of Antiriad (C64, 1986), Spellbound (C64, 1986)\n\n[Some other NES OSTs that defined its sound and/or used its features creatively](https://www.youtube.com/watch?v=c2HaUf_ZLuk&list=PLznWdSFQxsPvCD2BuLNQ1F4rfwwDr7DXF):\n\nKing Kong 2: Ikari no Megaton Punch (1986), Castlevania (1986),\n\n[Some other AY-3 OSTs that defined its sound and/or used the chip creatively](https://www.youtube.com/playlist?list=PLznWdSFQxsPunF_A7OjVYsOqJgRzesZ5m):\n\nThrust (ST, 1986), Solomon's Key (ARC, 1986, 3x AY-3, partially different), Ghosts 'n Goblins (CPC, 1986)(different), Spellbound (Spectrum, 1986)\n\n[Some other OSTs that defined the SN76489 (SMS & GG) sound](https://www.youtube.com/watch?v=HeZsFCDyGio&list=PLznWdSFQxsPuMvqZF463pulUN-zTv0rbR):\n\nAlex Kidd in Miracle World (1986), Fantasy Zone (SMS, 1986)\n\n[Some other OSTs that defined the YM2151/OPM sound and/or used the chip creatively](https://www.youtube.com/watch?v=uDBocIpW3Ls&list=PLznWdSFQxsPukP_JqUu8gsu2OMUkxlGbY):\n\nQuartet (ARC, 1986)\n\n[Some other YM2203/OPN OSTs that defined its sound and/or used the chip creatively](https://www.youtube.com/watch?v=SPvwV9svH64&list=PLznWdSFQxsPvujOSw1L-i356px2KCAXLs): Legendary Wings (ARC, 2x OPN, 1986)\n\n[Some other OPL & OPL2 OSTs that defined the chip's sound and/or used it creatively](https://www.youtube.com/playlist?list=PLznWdSFQxsPvzeMFJ7qaShPJvGzS1lNm8):\n\n[Breakthru](https://www.youtube.com/watch?v=gPNaZq4JlDM) (OPL1, 1986)\n\n\\---\n\nUp next: FM synth in the arcades entered a new era of polish and complexity, paired with well used PCM chips for added depth, realism and punch. The C64 saw groundbreaking new work, with early SID sample use and increasingly ambitious composition. On the MSX, Konami introduced the SCC wavetable chip, a precursor or sibling chip in a way to the PC Engine/TurboGrafx-16’s HuC6280, which also launched this year. Meanwhile, sample use on the NES became more refined, and Amiga music finally came into its own with the first tracker software and the size efficient .mod music file format - hinting at an entirely different future for game audio.\n\nThanks for reading!", "sentiment": "positive", "sentiment_score": 0.9994, "summary": "Reddit post: The Early Evolution of Sound and Music in Video Games: 1986", "source": "reddit", "date": "2025-06-18 02:30:27 UTC"}, {"headline": "Microsoft’s next-gen Xbox has an AMD chip inside and is ‘not locked to a single store’", "description": "[No text content – possibly a link/image post]", "url": "https://reddit.com/r/gamestechinfonews/comments/1lecqsa/microsofts_nextgen_xbox_has_an_amd_chip_inside/", "image_url": "", "full_content": "[No text content – possibly a link/image post]", "sentiment": "neutral", "sentiment_score": 0.0, "summary": "Reddit post: Microsoft’s next-gen Xbox has an AMD chip inside and is ‘not locked to a single store’", "source": "reddit", "date": "2025-06-18 02:30:06 UTC"}, {"headline": "[Hiring] [Remote] [CET plus or minus 3 HOURS] - Senior Microsoft PowerBI Developer at Proxify (💸 $50k-$80k)", "description": "Proxify is hiring a remote Senior Microsoft PowerBI Developer.\nCategory: Software Development\n💸Salary: $50k-$80k\n📍Location: Remote (CET +/- 3 HOURS)\n\n[See more and apply here!](https://remotive.com/re...", "url": "https://reddit.com/r/SoftwareEngineerJobs/comments/1lecpnx/hiring_remote_cet_plus_or_minus_3_hours_senior/", "image_url": "", "full_content": "Proxify is hiring a remote Senior Microsoft PowerBI Developer.\nCategory: Software Development\n💸Salary: $50k-$80k\n📍Location: Remote (CET +/- 3 HOURS)\n\n[See more and apply here!](https://remotive.com/remote-jobs/software-dev/senior-microsoft-powerbi-developer-1359476)", "sentiment": "neutral", "sentiment_score": 0.0, "summary": "Reddit post: [Hiring] [Remote] [CET plus or minus 3 HOURS] - Senior Microsoft PowerBI Developer at Proxify (💸 $50k-$80k)", "source": "reddit", "date": "2025-06-18 02:27:55 UTC"}, {"headline": "Please look at this HWINFO screenshot and help me (i9-12900k FPS)", "description": "Pretty terrible drops in FPS despite having a fairly high-end CPU. I bought it to never dip below 300 FPS in CSGO even at 0,1% lows, but it doesn't work anywhere close as good in CS2.\n\n  \nI know Amds ...", "url": "https://reddit.com/r/cs2/comments/1lecoh0/please_look_at_this_hwinfo_screenshot_and_help_me/", "image_url": "", "full_content": "Pretty terrible drops in FPS despite having a fairly high-end CPU. I bought it to never dip below 300 FPS in CSGO even at 0,1% lows, but it doesn't work anywhere close as good in CS2.\n\n  \nI know Amds 3D cache CPUs are great, but it feels overkill to upgrade when I already have a good CPU. Is there anything here that sticks out?", "sentiment": "positive", "sentiment_score": 0.9477, "summary": "Reddit post: Please look at this HWINFO screenshot and help me (i9-12900k FPS)", "source": "reddit", "date": "2025-06-18 02:25:34 UTC"}, {"headline": "There's a weird cultishness to the frutiger aero aesthetic fanbase", "description": "I only recently found out that there's names for the various aesthetics I saw growing up, the three main ones being Y2K Futurism, Frutiger Aero, and Frutiger Metro. The labels were thought up in the l...", "url": "https://reddit.com/r/rant/comments/1lecmfq/theres_a_weird_cultishness_to_the_frutiger_aero/", "image_url": "", "full_content": "I only recently found out that there's names for the various aesthetics I saw growing up, the three main ones being Y2K Futurism, Frutiger Aero, and Frutiger Metro. The labels were thought up in the late 2010s and while they are useful it's a bit hard not to feel like they're a symptom of over-obsession.\n\nI recently joined or at the very least regularly viewed the frutiger metro and frutiger aero subreddits and I don't mind people sharing photos of buildings or UI that utilises the aesthetics in question, but there's this strange cultishness particularly surrounding the frutiger aero aesthetic that centres around the idea of it depicting some kind of promised utopia that we never got. \n\nFrutiger Aero is the name for other company's attempts to match the style of the Windows Aero design language either directly or indirectly. Windows Aero looked cool but it was really just Microsoft showing off Vista's ability to make use of newer graphics cards with translucency effects, a wider colour gamut, and a few fancy animations.  \n\nAt a push the designers might've been inspired by <PERSON>'s optimistic attitude but at the end of the day it's all just marketing. The way some of the people talk on the subreddits though makes it sound like frutiger aero is the only aesthetic that should exist, that it's the only way we can ever feel inspired again and leave our current dystopia, and that designers shouldn't try anything new or risky. Yeah there's a grain of truth in the sense that minimalism has made a lot of things look really bland today but the way these guys talk you'd be forgiven for thinking that Frutiger Aero was some kind of magic bullet solution to all of society's problems. Some transparent glass here, some water effects there and, hey presto, we have utopia! \n\nMy personal take is that a lot of today's issues are down to people not wanting to take risks; we as consumers want what we ask for and companies want to give us *exactly* that and nothing else vs the 2000s where there was a bit more of a drive to innovate and a better understanding of what people *need* from their technology vs what they want. Companies didn't always get it right (seriously, what the fuck was the Nokia 7280?) but there was definitely more of a trend of new and exciting tech. There's an irony to people demanding frutiger aero and then claiming that this will solve the issue caused by people not wanting to take risks with something new and original.", "sentiment": "positive", "sentiment_score": 0.9889, "summary": "Reddit post: There's a weird cultishness to the frutiger aero aesthetic fanbase", "source": "reddit", "date": "2025-06-18 02:21:36 UTC"}, {"headline": "Microsoft account got locked and no way to unlock it.", "description": "Hello everyone. My microsoft got locked because of suspicious activity and there was no activity other than I was emailing at that time. The only way I can get back into my account is through a verifi...", "url": "https://reddit.com/r/microsoft365/comments/1lecly3/microsoft_account_got_locked_and_no_way_to_unlock/", "image_url": "", "full_content": "Hello everyone. My microsoft got locked because of suspicious activity and there was no activity other than I was emailing at that time. The only way I can get back into my account is through a verification code sent to my phone and when I try to do this. It doesn't work. It says please try another verification method.\n\nThere is no way to contact Microsoft support centre or customer service. The only way possible is to raise a question or ask for help on their website by signing in first which I am unable to do. Can someone please help.", "sentiment": "positive", "sentiment_score": 0.5574, "summary": "Reddit post: Microsoft account got locked and no way to unlock it.", "source": "reddit", "date": "2025-06-18 02:20:46 UTC"}, {"headline": "Complete Microsoft PowerPoint 365 Course with Copilot Intro", "description": "[No text content – possibly a link/image post]", "url": "https://reddit.com/r/FreeUdemyCoupons/comments/1leclol/complete_microsoft_powerpoint_365_course_with/", "image_url": "", "full_content": "[No text content – possibly a link/image post]", "sentiment": "neutral", "sentiment_score": 0.0, "summary": "Reddit post: Complete Microsoft PowerPoint 365 Course with Copilot Intro", "source": "reddit", "date": "2025-06-18 02:20:15 UTC"}, {"headline": "Complete Microsoft PowerPoint 365 Course with Copilot Intro", "description": "[No text content – possibly a link/image post]", "url": "https://reddit.com/r/udemyfreebies/comments/1lecloh/complete_microsoft_powerpoint_365_course_with/", "image_url": "", "full_content": "[No text content – possibly a link/image post]", "sentiment": "neutral", "sentiment_score": 0.0, "summary": "Reddit post: Complete Microsoft PowerPoint 365 Course with Copilot Intro", "source": "reddit", "date": "2025-06-18 02:20:15 UTC"}, {"headline": "PBIR preview", "description": "Hey folks,\nI’m planning to use the new PBIR version, however, it has some limitations that is in preview for a while. For example, deploying ir through deployment pipelines. Does anyone knows when Mic...", "url": "https://reddit.com/r/PowerBI/comments/1leclgq/pbir_preview/", "image_url": "", "full_content": "Hey folks,\nI’m planning to use the new PBIR version, however, it has some limitations that is in preview for a while. For example, deploying ir through deployment pipelines. Does anyone knows when Microsoft plans to fix this issue/bug ? We have a ci/cd in place using deployment pipelines, as git is not available for now.\n\nThanks", "sentiment": "positive", "sentiment_score": 0.4404, "summary": "Reddit post: PBIR preview", "source": "reddit", "date": "2025-06-18 02:19:49 UTC"}, {"headline": "Salário para um Programador C# Junior", "description": "Bom dia a todos.\n\n  \nTendo 1 ano de experiência e estando integrado bem nos projetos da empresa, qual seria o salário mais justo para se aceitar?\n\nAtualmente estou a rondar os 900€ já com subsidio de ...", "url": "https://reddit.com/r/devpt/comments/1lecknb/sal<PERSON><PERSON>_para_um_programador_c_junior/", "image_url": "", "full_content": "Bom dia a todos.\n\n  \nTendo 1 ano de experiência e estando integrado bem nos projetos da empresa, qual seria o salário mais justo para se aceitar?\n\nAtualmente estou a rondar os 900€ já com subsidio de alimentação (liquidos) e acaba em agosto o contrato, acredito que queiram renovar.\n\nAceitei esses 900€ já c/ SA porque não tinha experiência e sabia que tinha que começar por algum lado, mas neste momento sinto que já não sou só para 900€.  \nLogo, quero uma opinião vossa, 900€ para um Junior é pouco?\n\nSeguem as infos de trabalho:\n\n\\- Dias úteis: 09h às 19h (2h almoço - 8h de trabalho) + Sábados das 09h ao 12h\n\n\\- Tecnologias utilizadas:\n\n1. C# + Windows Forms\n2. Microsoft SQL Server\n3. Crystal Reports\n\n  \nConsiderando isto, se fossem vocês o que pretendiam de salário? Tendo em conta o horário, as tecnologias e a experiência?\n\nAbraço a todos.", "sentiment": "neutral", "sentiment_score": 0.0, "summary": "Reddit post: <PERSON><PERSON><PERSON> para um Programador C# Junior", "source": "reddit", "date": "2025-06-18 02:18:22 UTC"}, {"headline": "Defender can`t reach enpoint URLs", "description": "Hello everyone,\n\nwe are about to onboard our servers to defender and are now starting with a testgroup.\n\nIf we use the MDE Client Analyzer we can see that the servers are not able to connect to the De...", "url": "https://reddit.com/r/DefenderATP/comments/1lecki2/defender_cant_reach_enpoint_urls/", "image_url": "", "full_content": "Hello everyone,\n\nwe are about to onboard our servers to defender and are now starting with a testgroup.\n\nIf we use the MDE Client Analyzer we can see that the servers are not able to connect to the Defender Cloud service.\n\nhttps://preview.redd.it/27z450jtin7f1.png?width=2441&format=png&auto=webp&s=b0d873ebb29ec0df06534f710deb7f99d5d7f604\n\nThe Firewall is configured and we can see that the traffic is passed, however it is timed out.\n\nDigging deeper, i´m not able to resolve the adresses. They are not resolvable at all, even if tried through websites for DNS lookup. Am i stupid or is this something Microsoft messed up ?\n\nURLs:\n\n* [https://edr-neu-eu.endpoint.security.microsoft.com/edr/commands/test](https://edr-neu-eu.endpoint.security.microsoft.com/edr/commands/test)\n* [https://mdav-eu.endpoint.security.microsoft.com/mdav/wdcp.svc/heartbeat](https://mdav-eu.endpoint.security.microsoft.com/mdav/wdcp.svc/heartbeat)\n* [https://edr-weu-eu.endpoint.security.microsoft.com/edr/commands/test](https://edr-weu-eu.endpoint.security.microsoft.com/edr/commands/test)\n\n\n\n||\n||\n||", "sentiment": "negative", "sentiment_score": -0.7772, "summary": "Reddit post: Defender can`t reach enpoint URLs", "source": "reddit", "date": "2025-06-18 02:18:06 UTC"}, {"headline": "Salário para um Programador C# Junior", "description": "Bom dia a todos.\n\n  \nTendo 1 ano de experiência e estando integrado bem nos projetos da empresa, qual seria o salário mais justo para se aceitar?\n\nAtualmente estou a rondar os 900€ já com subsidio de ...", "url": "https://reddit.com/r/PTOrdenado/comments/1leckg9/salá<PERSON>_para_um_programador_c_junior/", "image_url": "", "full_content": "Bom dia a todos.\n\n  \nTendo 1 ano de experiência e estando integrado bem nos projetos da empresa, qual seria o salário mais justo para se aceitar?\n\nAtualmente estou a rondar os 900€ já com subsidio de alimentação (liquidos) e acaba em agosto o contrato, acredito que queiram renovar.\n\nAceitei esses 900€ já c/ SA porque não tinha experiência e sabia que tinha que começar por algum lado, mas neste momento sinto que já não sou só para 900€.  \nLogo, quero uma opinião vossa, 900€ para um Junior é pouco?\n\nSeguem as infos de trabalho:\n\n\\- Dias úteis: 09h às 19h (2h almoço - 8h de trabalho) + Sábados das 09h ao 12h\n\n\\- Tecnologias utilizadas:\n\n1. C# + Windows Forms\n2. Microsoft SQL Server\n3. Crystal Reports\n\n  \nConsiderando isto, se fossem vocês o que pretendiam de salário? Tendo em conta o horário, as tecnologias e a experiência?\n\nAbraço a todos.", "sentiment": "neutral", "sentiment_score": 0.0, "summary": "Reddit post: <PERSON><PERSON><PERSON> para um Programador C# Junior", "source": "reddit", "date": "2025-06-18 02:18:02 UTC"}, {"headline": "A hard truth for people who can't digest it", "description": "[No text content – possibly a link/image post]", "url": "https://reddit.com/r/hyderabad/comments/1lecjpb/a_hard_truth_for_people_who_cant_digest_it/", "image_url": "", "full_content": "[No text content – possibly a link/image post]", "sentiment": "positive", "sentiment_score": 0.2263, "summary": "Reddit post: A hard truth for people who can't digest it", "source": "reddit", "date": "2025-06-18 02:16:40 UTC"}, {"headline": "How Food & Beverage Companies Use ERP to Ensure FDA Compliance", "description": "The food and beverage industry operates in a highly regulated environment, where compliance with the Food and Drug Administration (FDA) standards is non-negotiable. From farm to fork, companies must e...", "url": "https://reddit.com/r/u_tridentinfocorp/comments/1lecjar/how_food_beverage_companies_use_erp_to_ensure_fda/", "image_url": "", "full_content": "The food and beverage industry operates in a highly regulated environment, where compliance with the Food and Drug Administration (FDA) standards is non-negotiable. From farm to fork, companies must ensure safety, traceability, and quality to protect consumers and maintain brand reputation. Enterprise Resource Planning (ERP) systems have emerged as indispensable tools for achieving these goals, streamlining operations, and ensuring compliance. This article explores how food and beverage companies leverage ERP systems to meet FDA requirements, with a focus on ERP implementation and consulting services, particularly for the hospitality and food industry. \n\n# The Importance of FDA Compliance in the Food & Beverage Industry\n\nThe FDA (Food & Drug Administration) enforces stringent regulations, such as the Food Safety Modernization Act (FSMA), Hazard Analysis and Critical Control Points (HACCP), and Current Good Manufacturing Practices (CGMP), to ensure food safety. Non-compliance can lead to costly recalls, legal penalties, and reputational damage. ERP systems provide a centralized platform to manage compliance, offering real-time data, automation, and traceability to meet these standards.\n\n# How ERP Systems Facilitate FDA Compliance\n\nERP systems integrate different types of business processes—production, inventory, supply chain, and quality control—into a single platform, enabling food and beverage companies to address FDA requirements efficiently. Below are key ways ERP systems ensure compliance:\n\n* **End-to-End Traceability**: FDA regulations mandate bi-directional traceability to track ingredients from suppliers to finished products. ERP systems like Microsoft Dynamics 365 provide lot and batch tracking, allowing companies to trace every ingredient in real-time. This is critical for rapid response during recalls, minimizing risks to consumers and the business.\n\n* **Quality Assurance and Control**: ERP systems enable automated quality checks at every stage of production. For instance, they monitor raw materials, work-in-progress, and final products to ensure consistency and adherence to FDA’s CGMP standards. Features like quarantine procedures for failed batches prevent non-compliant products from reaching the market.\n\n* **Recipe and Formula Management**: Maintaining standardized recipes and formulas is essential for compliance with FDA categorize requirements. ERP systems store and manage recipes, ensuring accurate nutritional data and allergen information. This reduces errors in categorize, which could otherwise lead to regulatory violations.\n\n* **Regulatory Reporting and Audits**: ERP systems generate detailed reports for regulatory audits, including batch tickets, inspection records, and compliance documentation. Automated reporting saves time and ensures accuracy, helping companies stay prepared for FDA inspections.\n\n* **Preventing Food Fraud**: The FDA requires controls to prevent economically motivated adulteration (EMA), such as substituting valuable ingredients with cheaper alternatives. ERP systems establish approved supplier lists, track ingredient sourcing, and flag high-risk ingredients for testing, reducing the risk of fraud.\n\n* **Real-Time Data for Decision-Making**: ERP systems provide real-time insights into production, inventory, and supply chain operations. This helps companies respond proactively to compliance issues, such as detecting deviations in production processes or anticipating supply chain disruptions.\n\n# ERP Implementation and Consulting Services\n\nTexas is a hub for the food and beverage industry, with a growing demand for ERP solutions tailored to hospitality and manufacturing. ERP implementation services, play a critical role in helping businesses adopt these systems effectively. Companies like Trident Info Corp offer specialized expertise to ensure seamless ERP deployment.\n\n* **Needs Assessment and Customization**: Consultants conduct thorough needs analyses to identify the specific requirements of food and beverage businesses. They customize ERP systems to align with unique processes, such as recipe management or allergen control, ensuring compliance and operational efficiency.\n\n* **Data Migration and Integration**: ERP implementation involves migrating data from legacy systems to the new platform. Texas-based consultants ensure smooth ERP migration services, integrating ERP with existing tools like point-of-sale (POS) systems, warehouse management, and Microsoft Dynamics 365 CRM for enhanced customer relationship management.\n\n* **Training and Support**: Successful ERP adoption requires employee training and ongoing support. Consulting firms in Texas provide comprehensive training programs and 24/7 support to address technical issues, ensuring businesses maximize their ERP investment.\n\n* **Cloud-Based Solutions**: Many Texas-based ERP providers, including Trident Info Corp, leverage cloud-based ERP systems like Microsoft Dynamics 365 Business Central. These solutions offer scalability, remote access, and automatic updates, reducing the need for on-premises infrastructure.\n\n# ERP for Hospitality & Food Industry\n\nThe hospitality and food industry in Texas faces unique challenges, such as managing perishable inventory, fluctuating consumer demand, and strict compliance requirements. ERP systems designed for this sector address these issues while enhancing customer service and profitability.\n\n* **Inventory Management**: ERP systems provide real-time visibility into stock levels, reducing waste from overstocking or spoilage. Features like automatic reordering and barcode scanning ensure accurate inventory tracking, critical for FDA compliance.\n* **Integration with POS Systems**: For restaurants and food service businesses, ERP systems integrate with POS systems to synchronize sales, inventory, and customer data. This improves order accuracy and supports compliance with FDA labeling and safety standards.\n\n* **Dynamic Scheduling**: ERP systems enable real-time production scheduling based on sales trends, ensuring businesses meet consumer demand without compromising quality or compliance.\n\n# ERP Implementation & Migration\n\nERP implementation and migration services in Texas, USA, are tailored to minimize disruptions and maximize ROI. Whether upgrading from legacy systems or adopting ERP for the first time, businesses benefit from expert guidance.\n\n* **Phased Implementation**: Consultants use a phased approach to ERP implementation, starting with critical modules like inventory and compliance management. This reduces downtime and allows employees to adapt gradually.\n\n* **Seamless Migration**: ERP migration services ensure data integrity during the transition. Texas-based providers like Trident Info Corp use proven methodologies to migrate data securely, preserving historical records for FDA audits.\n\n* **Scalability for Growth**: ERP systems are designed to scale with business growth, accommodating increased data volumes and additional users. This ensures long-term compliance and efficiency as companies expand.\n\n# Microsoft Dynamics 365 CRM Consultant in Texas, USA\n\nMicrosoft Dynamics 365 CRM consultants in Texas, USA, enhance ERP systems by integrating customer relationship management capabilities. This is particularly valuable for food and beverage companies aiming to improve customer loyalty and service.\n\n* **Centralized Customer Data**: Dynamics 365 CRM centralizes customer information, enabling personalized marketing and targeted campaigns. This supports compliance by ensuring accurate order information and delivery tracking.\n\n* **Enhanced Customer Service**: Real-time access to customer preferences and order history allows businesses to resolve issues quickly, maintaining trust and compliance with FDA standards.", "sentiment": "positive", "sentiment_score": 0.999, "summary": "Reddit post: How Food & Beverage Companies Use ERP to Ensure FDA Compliance", "source": "reddit", "date": "2025-06-18 02:15:53 UTC"}, {"headline": "Microsoft account got locked and no way to unlock it.", "description": "Hello everyone. \nMy microsoft got locked because of suspicious activity and there was no activity other than I was emailing at that time. The only way I can get back into my account is through a verif...", "url": "https://reddit.com/r/MicrosoftOutlook/comments/1lecja1/microsoft_account_got_locked_and_no_way_to_unlock/", "image_url": "", "full_content": "Hello everyone. \nMy microsoft got locked because of suspicious activity and there was no activity other than I was emailing at that time. The only way I can get back into my account is through a verification code sent to my phone and when Itry to do this. It doesn't work. It says please try another verification method.\nThere is no way to contact Microsoft support centre or customer service. The only way possible is to raise a question or ask for help on their website by signing in first which I am unable to do. \nCan someone please help.", "sentiment": "positive", "sentiment_score": 0.5574, "summary": "Reddit post: Microsoft account got locked and no way to unlock it.", "source": "reddit", "date": "2025-06-18 02:15:51 UTC"}, {"headline": "Complete Microsoft Excel 365 Masterclass with Copilot Intro", "description": "[No text content – possibly a link/image post]", "url": "https://reddit.com/r/FreeUdemyCoupons/comments/1lecij0/complete_microsoft_excel_365_masterclass_with/", "image_url": "", "full_content": "[No text content – possibly a link/image post]", "sentiment": "positive", "sentiment_score": 0.4588, "summary": "Reddit post: Complete Microsoft Excel 365 Masterclass with Copilot Intro", "source": "reddit", "date": "2025-06-18 02:14:23 UTC"}], "sources_count": {"blog": 0, "google_search": 20, "reddit": 20, "youtube": 0}}