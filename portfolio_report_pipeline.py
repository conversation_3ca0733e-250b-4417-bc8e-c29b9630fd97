import os
import json
from datetime import datetime, timedelta
from collections import defaultdict
from sklearn.cluster import KMeans
from sklearn.feature_extraction.text import TfidfVectorizer
from vaderSentiment.vaderSentiment import SentimentIntensityAnalyzer
from newspaper import Article
import requests
import google.generativeai as genai

# ------------------------ Step 1: Fetch News & Sentiment Analysis ------------------------

def fetch_news(api_key, query, from_date, to_date):
    url = 'https://newsapi.org/v2/everything'
    params = {
        'q': query,
        'from': from_date,
        'to': to_date,
        'language': 'en',
        'sortBy': 'relevancy',
        'pageSize': 100,
        'apiKey': api_key
    }
    response = requests.get(url, params=params)
    data = response.json()
    return data.get('articles', [])

def analyze_sentiment(articles):
    analyzer = SentimentIntensityAnalyzer()
    analyzed = []
    for article in articles:
        title = article.get('title', '')
        description = article.get('description', '')
        url = article.get('url', '')
        image_url = article.get('urlToImage', '')
        published_at = article.get('publishedAt', '')

        # Attempt to extract full article content
        full_content = ''
        try:
            news_article = Article(url)
            news_article.download()
            news_article.parse()
            full_content = news_article.text
        except Exception:
            full_content = description  # Fallback to description

        content = f"{title}. {full_content}"
        score = analyzer.polarity_scores(content)['compound']
        sentiment = 'positive' if score >= 0.05 else 'negative' if score <= -0.05 else 'neutral'

        analyzed.append({
            'date': published_at,
            'headline': title,
            'description': description,
            'url': url,
            'image_url': image_url,
            'full_content': full_content,
            'sentiment': sentiment,
            'sentiment_score': score
        })
    return analyzed

# ------------------------ Step 2: Cluster & Summarize Articles ------------------------

def summarize_articles(input_path, output_path):
    with open(input_path, 'r', encoding='utf-8') as f:
        data = json.load(f)

    articles = data.get('articles', [])

    texts = []
    article_map = {}
    for idx, article in enumerate(articles):
        content = f"{article.get('headline', '')} {article.get('description', '')} {article.get('full_content', '')}"
        texts.append(content)
        article_map[idx] = article

    # Vectorize
    vectorizer = TfidfVectorizer(stop_words='english')
    X = vectorizer.fit_transform(texts)

    # Cluster
    num_clusters = 5
    kmeans = KMeans(n_clusters=num_clusters, random_state=0)
    kmeans.fit(X)
    labels = kmeans.labels_

    # Group by cluster
    clustered_articles = defaultdict(list)
    for idx, label in enumerate(labels):
        clustered_articles[label].append(article_map[idx])

    # Summarize using Gemini
    os.environ["GOOGLE_API_KEY"] = os.getenv("GOOGLE_API_KEY", "AIzaSyA8oHeYPwEUEkKIwuQcC5FfebamHoGDbVw")  # replace default or set env
    genai.configure(api_key=os.environ["GOOGLE_API_KEY"])
    model = genai.GenerativeModel('gemini-2.0-flash')

    summarized_clusters = []
    for label, articles in clustered_articles.items():
        combined_text = "\n".join(
            f"{article.get('headline', '')}\n{article.get('description', '')}\n{article.get('full_content', '')}"
            for article in articles
        )
        prompt = f"Summarize the following news articles:\n{combined_text}"
        response = model.generate_content(prompt)
        summary = response.text
        summarized_clusters.append({
            "cluster_id": label,
            "summary": summary,
            "articles": articles
        })

    # Group by date
    organized_data = defaultdict(list)
    for cluster in summarized_clusters:
        for article in cluster['articles']:
            date_str = article.get('date', '')
            try:
                date = datetime.strptime(date_str, '%Y-%m-%dT%H:%M:%SZ').date()
            except ValueError:
                continue
            organized_data[str(date)].append({
                "headline": article.get('headline', ''),
                "description": article.get('description', ''),
                "url": article.get('url', ''),
                "image_url": article.get('image_url', ''),
                "full_content": article.get('full_content', ''),
                "sentiment": article.get('sentiment', ''),
                "sentiment_score": article.get('sentiment_score', 0.0),
                "summary": cluster['summary']
            })

    with open(output_path, 'w', encoding='utf-8') as f:
        json.dump(organized_data, f, indent=4)

# ------------------------ Step 3: Generate Executive Report ------------------------

def generate_executive_report(input_path, output_html_path):
    with open(input_path, 'r', encoding='utf-8') as f:
        news_data = json.load(f)

    if not news_data:
        print("No data to analyze.")
        return

    all_articles = [a for articles in news_data.values() for a in articles]
    company_name = all_articles[0].get('headline', 'Company').split(' ')[0]
    from_date = min(news_data.keys())
    to_date = max(news_data.keys())

    filtered_articles = [a for a in all_articles if a['sentiment'] in ['positive', 'neutral']]

    prompt = f"""
You are "Portfolio Performance Analyst AI."

**Objective:**
Generate a concise, insightful, and actionable executive-level portfolio update email for venture investors, using the provided company-specific JSON news data. Prioritize positive or neutral sentiment articles for thematic synthesis.

**Audience:**
Sophisticated, time-constrained investors demanding clarity, precision, and strategic foresight.

**Input Data:**
JSON object: {json.dumps(filtered_articles)}

**Communication Mandate: Top-Tier Consultant Standard**
* Tone: Executive presence—authoritative, confident, composed.
* Structure: Top-down (key message first), MECE.
* Language: Extremely concise. No fluff.
* Focus: Strategic insight, proactive analysis, forward-looking implications.

**Output: Single, Professionally Formatted Markdown Email**
"""

    model = genai.GenerativeModel('gemini-2.0-flash')
    response = model.generate_content(prompt)
    markdown_report = response.text

    html_content = f"<html><body><pre>{markdown_report}</pre></body></html>"
    with open(output_html_path, 'w', encoding='utf-8') as f:
        f.write(html_content)

# ------------------------ Main Workflow ------------------------

def main():
    company_name = input("Enter company name (in double quotes): ").strip()
    api_key = '4f937e60d13441ab90dc10c3006d2a81'

    to_date = datetime.now().date()
    from_date = to_date - timedelta(days=23)
    print(f"Fetching news for {company_name} from {from_date} to {to_date}...")

    articles = fetch_news(api_key, company_name, from_date.isoformat(), to_date.isoformat())
    if not articles:
        print("No news articles found.")
        return

    print("Analyzing sentiment...")
    analyzed = analyze_sentiment(articles)

    raw_output = f"{company_name.lower().replace(' ', '_').replace('.', '')}_news_sentiment.json"
    with open(raw_output, 'w', encoding='utf-8') as f:
        json.dump({
            "company": company_name,
            "from_date": str(from_date),
            "to_date": str(to_date),
            "articles": analyzed
        }, f, indent=4)

    print("Clustering and summarizing...")
    summarized_output = f"summarized_{company_name.lower().replace(' ', '_').replace('.', '')}.json"
    summarize_articles(raw_output, summarized_output)

    print("Generating final executive report...")
    html_report = f"report_{company_name.lower().replace(' ', '_').replace('.', '')}.html"
    generate_executive_report(summarized_output, html_report)

    print(f"✅ Report saved to {html_report}")

if __name__ == "__main__":
    main()
