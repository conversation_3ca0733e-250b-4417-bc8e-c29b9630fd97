# Portfolio Performance Analyzer

A comprehensive system that processes data from 4 Python scripts to collect company information, stores it, and generates executive-level portfolio reports using Gemini AI with HTML formatted output for single or multiple companies.

## 🚀 Features

- **Multi-Source Data Collection**: Gathers data from 4 different sources:
  - Blog posts (company official blogs)
  - Google search results (news articles)
  - Reddit posts (social media sentiment)
  - YouTube videos (video content analysis)

- **Intelligent Data Aggregation**: Combines and standardizes data from all sources with sentiment analysis

- **AI-Powered Analysis**: Uses Gemini AI to generate executive-level portfolio reports

- **Professional HTML Output**: Creates beautifully formatted HTML reports suitable for investors

- **Multi-Company Support**: Handles single or multiple companies with combined reporting

## 📁 Project Structure

```
elevation_ai/news summarizer/
├── main.py                          # Main orchestrator script
├── config.py                        # Configuration and API keys
├── data_aggregator.py               # Data standardization and aggregation
├── gemini_processor.py              # Gemini AI integration and HTML generation
├── blog_post.py                     # Blog post scraping
├── google_search.py                 # Google search API integration
├── reddit_scrap.py                  # Reddit API integration
├── enhanced_youtube_scraping.py     # YouTube API integration
├── test_multi_company.py            # Multi-company testing script
├── demo_report.html                 # Example output report
└── venv/                           # Virtual environment
```

## 🛠️ Installation & Setup

### Prerequisites
- Python 3.8+
- Virtual environment (already set up in `venv/`)

### API Keys Required
Update `config.py` with your API keys:
- **Gemini API Key**: For AI analysis
- **Google Search API Key & CSE ID**: For news search
- **YouTube API Key**: For video content
- **Reddit API Credentials**: For social media data

### Dependencies
All required packages are already installed in the virtual environment:
- `vaderSentiment`: Sentiment analysis
- `praw`: Reddit API
- `google-api-python-client`: Google/YouTube APIs
- `youtube-transcript-api`: Video transcripts
- `beautifulsoup4`: Web scraping
- `requests`: HTTP requests

## 🎯 Usage

### Basic Usage
```bash
# Activate virtual environment and run
venv/bin/python main.py
```

### Interactive Process
1. **Enter Company Names**: Input one or multiple companies
   - Single: `Tesla`
   - Multiple: `Tesla, Apple, Microsoft`

2. **Data Collection**: System automatically:
   - Scrapes company blog posts
   - Searches Google for recent news
   - Collects Reddit discussions
   - Analyzes YouTube videos

3. **Report Generation**: Creates professional HTML reports with:
   - Executive summary
   - Key developments & themes
   - Sentiment analysis
   - Strategic implications for investors

### Example Commands
```bash
# Single company analysis
venv/bin/python main.py
# Enter: Tesla

# Multi-company demo
venv/bin/python test_multi_company.py
```

## 📊 Output Examples

### Single Company Report
- File: `portfolio_report_tesla.html`
- Contains: Comprehensive analysis for one company

### Multi-Company Report
- File: `portfolio_report_multi_company_YYYYMMDD_HHMMSS.html`
- Contains: Combined analysis with table of contents

### Data Files
- `aggregated_[company].json`: Raw aggregated data
- Professional HTML reports with CSS styling

## 🎨 Report Features

### Executive-Level Format
- **Top-Tier Consultant Standard**: McKinsey/BCG/Bain style
- **MECE Structure**: Mutually Exclusive, Collectively Exhaustive
- **Concise Language**: No fluff, authoritative tone
- **Strategic Focus**: Forward-looking implications

### Content Sections
1. **Headline Lead**: One impactful summary sentence
2. **Executive Summary**: 3 critical takeaways
3. **Key Developments**: 2-4 synthesized themes
4. **Sentiment Analysis**: Overall sentiment with rationale
5. **Strategic Implications**: Investment-focused insights

### Visual Design
- Professional CSS styling
- Responsive design
- Color-coded sentiment indicators
- Clean, readable typography

## 🔧 Configuration

### API Keys Setup
Edit `config.py`:
```python
GEMINI_API_KEY = 'your_gemini_key'
GOOGLE_SEARCH_API_KEY = 'your_google_key'
GOOGLE_CSE_ID = 'your_cse_id'
YOUTUBE_API_KEY = 'your_youtube_key'
REDDIT_CLIENT_ID = 'your_reddit_id'
REDDIT_CLIENT_SECRET = 'your_reddit_secret'
```

### Customization Options
- **Date Range**: Modify `DEFAULT_DATE_RANGE_DAYS` in config
- **Article Limits**: Adjust limits in individual scripts
- **Sentiment Thresholds**: Modify in `data_aggregator.py`

## 🚨 Error Handling

The system includes robust error handling:
- **API Failures**: Graceful fallbacks and error messages
- **Network Issues**: Timeout handling and retries
- **Missing Data**: Continues processing with available sources
- **Gemini Errors**: Fallback HTML reports with raw data

## 📈 Data Sources

### Blog Posts (`blog_post.py`)
- Finds official company blogs using Gemini AI
- Scrapes recent posts with content extraction
- Handles various blog formats and structures

### Google Search (`google_search.py`)
- Uses Google Custom Search API
- Filters for recent, relevant news articles
- Extracts full article content when possible

### Reddit (`reddit_scrap.py`)
- Searches across all subreddits
- Collects post titles, content, and metadata
- Filters for relevant discussions

### YouTube (`enhanced_youtube_scraping.py`)
- Searches for company-related videos
- Extracts transcripts for content analysis
- Filters by channel subscriber count (100K+)

## 🎯 Use Cases

### Investment Analysis
- Portfolio company monitoring
- Due diligence research
- Market sentiment tracking
- Competitive intelligence

### Business Intelligence
- Brand monitoring
- Crisis management
- Strategic planning
- Market research

## 🔮 Future Enhancements

- **Real-time Updates**: Scheduled report generation
- **Advanced Analytics**: Trend analysis and predictions
- **Custom Templates**: Industry-specific report formats
- **Integration APIs**: Connect with portfolio management tools
- **Mobile Optimization**: Responsive design improvements

## 📞 Support

For issues or questions:
1. Check error messages in terminal output
2. Verify API keys in `config.py`
3. Ensure virtual environment is activated
4. Review individual script outputs for debugging

## 📄 License

This project is for portfolio analysis and research purposes.

---

**Ready to analyze your portfolio companies? Run `venv/bin/python main.py` to get started!**
