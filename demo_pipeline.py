#!/usr/bin/env python3
"""
Demo script to show the Market Intelligence Pipeline working flow

This script demonstrates the complete pipeline without needing the full scraping workflow.
"""

import os
import tempfile
from datetime import datetime

def create_sample_report(company_name: str = "Tesla") -> str:
    """Create a sample HTML report for demonstration"""
    html_content = f"""
    <!DOCTYPE html>
    <html>
    <head>
        <title>Market Intelligence Report - {company_name}</title>
        <meta charset="UTF-8">
    </head>
    <body>
        <h1>Market Intelligence Report for {company_name}</h1>
        
        <h2>Executive Summary</h2>
        <p>This comprehensive market intelligence report provides insights into {company_name}'s 
        current market position, competitive landscape, and strategic opportunities. Our analysis 
        reveals strong performance indicators and growth potential in key market segments.</p>
        
        <h2>Market Analysis</h2>
        <p>The market analysis for {company_name} shows several key trends:</p>
        <ul>
            <li>Increased demand for electric vehicles and sustainable transportation</li>
            <li>Growing competition in the EV market from traditional automakers</li>
            <li>Expansion opportunities in emerging markets and energy storage</li>
            <li>Regulatory support for clean energy initiatives</li>
        </ul>
        
        <h2>Financial Performance</h2>
        <p>Recent financial data indicates {company_name} has maintained strong revenue 
        growth with improving operational efficiency. Key metrics include:</p>
        <ul>
            <li>Revenue growth: 25% year-over-year</li>
            <li>Gross margin: 18.7%</li>
            <li>Market capitalization: $800B+</li>
            <li>Vehicle deliveries: 1.8M+ annually</li>
        </ul>
        
        <h2>Strategic Initiatives</h2>
        <p>Based on our analysis, {company_name} is pursuing several strategic initiatives:</p>
        <ol>
            <li>Expansion of Gigafactory network globally</li>
            <li>Development of autonomous driving technology</li>
            <li>Growth in energy storage and solar business</li>
            <li>Entry into new vehicle segments and markets</li>
        </ol>
        
        <h2>Competitive Landscape</h2>
        <p>The competitive environment for {company_name} includes:</p>
        <ul>
            <li>Traditional automakers transitioning to electric vehicles</li>
            <li>New EV startups with innovative approaches</li>
            <li>Technology companies entering the mobility space</li>
            <li>Chinese EV manufacturers expanding globally</li>
        </ul>
        
        <h2>Risk Assessment</h2>
        <p>Key risks that {company_name} should monitor include:</p>
        <ul>
            <li>Supply chain disruptions and raw material costs</li>
            <li>Regulatory changes in key markets</li>
            <li>Intensifying competition and price pressure</li>
            <li>Technology and manufacturing execution risks</li>
        </ul>
        
        <h2>Investment Outlook</h2>
        <p>The investment outlook for {company_name} remains positive, supported by:</p>
        <ul>
            <li>Strong brand recognition and customer loyalty</li>
            <li>Technological leadership in EVs and batteries</li>
            <li>Expanding total addressable market</li>
            <li>Diversified revenue streams beyond automotive</li>
        </ul>
        
        <h2>Recommendations</h2>
        <p>Our recommendations for {company_name} include:</p>
        <ol>
            <li>Continue investing in manufacturing capacity and efficiency</li>
            <li>Accelerate autonomous driving development and deployment</li>
            <li>Expand energy business and grid-scale storage solutions</li>
            <li>Strengthen supply chain resilience and vertical integration</li>
            <li>Maintain focus on innovation and technological differentiation</li>
        </ol>
        
        <h2>Conclusion</h2>
        <p>{company_name} is well-positioned to capitalize on the global transition to 
        sustainable transportation and energy. The company's integrated approach to 
        electric vehicles, energy storage, and autonomous technology provides multiple 
        avenues for growth and value creation.</p>
        
        <footer>
            <p>Report generated on {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
            <p>Market Intelligence Pipeline - Comprehensive Analysis</p>
        </footer>
    </body>
    </html>
    """
    
    # Create temporary file
    with tempfile.NamedTemporaryFile(mode='w', suffix='.html', delete=False, encoding='utf-8') as f:
        f.write(html_content)
        return f.name

def demo_pipeline_flow():
    """Demonstrate the complete pipeline flow"""
    print("🚀 Market Intelligence Pipeline - Demo Flow")
    print("=" * 60)
    
    company_name = "Tesla"
    sample_file = None
    
    try:
        # Step 1: Create sample report
        print(f"\n📝 Step 1: Creating sample HTML report for {company_name}...")
        sample_file = create_sample_report(company_name)
        print(f"✅ Sample report created: {sample_file}")
        
        # Step 2: Test individual components
        print(f"\n🧪 Step 2: Testing individual pipeline components...")
        
        # Test GCS Storage Manager
        print(f"\n📦 Testing GCS Storage Manager...")
        try:
            from gcs_storage_manager import GCSStorageManager
            gcs_manager = GCSStorageManager()
            print("✅ GCS Storage Manager initialized")
            
            # Test upload (will show error without credentials, but demonstrates the flow)
            upload_result = gcs_manager.upload_report(sample_file, company_name)
            if upload_result['success']:
                print(f"✅ Report uploaded to: {upload_result['gcs_url']}")
            else:
                print(f"⚠️ Upload failed (expected without credentials): {upload_result.get('error', 'Unknown error')[:100]}...")
        except Exception as e:
            print(f"❌ GCS Storage Manager error: {e}")
        
        # Test Vertex AI Embedding Service
        print(f"\n🧠 Testing Vertex AI Embedding Service...")
        try:
            from vertex_ai_embedding_service import VertexAIEmbeddingService
            embedding_service = VertexAIEmbeddingService()
            print("✅ Vertex AI Embedding Service initialized")
            
            # Test embedding generation
            embedding_result = embedding_service.process_html_file(sample_file, company_name)
            if embedding_result['success']:
                print(f"✅ Embeddings generated:")
                print(f"   Chunks: {embedding_result['num_chunks']}")
                print(f"   Embedding dimension: {embedding_result['embedding_dimension']}")
                print(f"   Text length: {embedding_result['text_length']} characters")
            else:
                print(f"❌ Embedding generation failed: {embedding_result.get('error')}")
        except Exception as e:
            print(f"❌ Vertex AI Embedding Service error: {e}")
        
        # Test Vertex AI Matching Engine
        print(f"\n🔍 Testing Vertex AI Matching Engine...")
        try:
            from vertex_ai_matching_engine import VertexAIMatchingEngineManager
            matching_engine = VertexAIMatchingEngineManager()
            print("✅ Vertex AI Matching Engine Manager initialized")
            
            # Test datapoint creation
            sample_embedding = [0.1] * 768
            datapoint = matching_engine.create_datapoint(
                embedding=sample_embedding,
                company=company_name,
                date="2024-01-15",
                timestamp="2024-01-15T14:30:00",
                gcs_url=f"gs://test-bucket/{company_name}/2024-01-15/14-30-00.html",
                chunk_id=0,
                text_content="Sample text content for testing"
            )
            print(f"✅ Datapoint created with ID: {datapoint['id']}")
        except Exception as e:
            print(f"❌ Vertex AI Matching Engine error: {e}")
        
        # Test Complete Pipeline
        print(f"\n🎯 Step 3: Testing complete pipeline integration...")
        try:
            from market_intelligence_pipeline import MarketIntelligencePipeline
            
            # Try to initialize pipeline
            try:
                pipeline = MarketIntelligencePipeline(enable_deduplication=False)  # Disable dedup for demo
                print("✅ Complete Pipeline initialized")
                
                # Health check
                health = pipeline.health_check()
                print(f"Pipeline health status: {health['overall_status']}")
                
                # Process the sample report
                print(f"\n🚀 Processing sample report through complete pipeline...")
                result = pipeline.process_report(sample_file, company_name)
                
                if result['success']:
                    print(f"🎉 Pipeline processing completed successfully!")
                    print(f"   Duration: {result.get('duration_seconds', 0):.2f} seconds")
                    print(f"   Chunks processed: {result.get('chunks_processed', 0)}")
                    print(f"   Vectors stored: {result.get('vectors_stored', 0)}")
                    
                    # Show step results
                    print(f"\n📋 Pipeline steps:")
                    for step_name, step_result in result.get('steps', {}).items():
                        status = "✅" if step_result.get('success', False) else "❌"
                        print(f"   {status} {step_name}")
                else:
                    print(f"⚠️ Pipeline processing completed with issues:")
                    print(f"   Error: {result.get('error')}")
                    print(f"\n📋 Pipeline steps:")
                    for step_name, step_result in result.get('steps', {}).items():
                        status = "✅" if step_result.get('success', False) else "❌"
                        print(f"   {status} {step_name}")
                        
            except Exception as e:
                print(f"⚠️ Pipeline initialization failed: {e}")
                print("   This is expected without proper GCP configuration")
                
        except ImportError as e:
            print(f"❌ Pipeline import error: {e}")
        
        # Summary
        print(f"\n" + "=" * 60)
        print(f"🎯 Demo Summary")
        print(f"=" * 60)
        print(f"✅ Sample HTML report created and processed")
        print(f"✅ Individual components tested")
        print(f"✅ Pipeline flow demonstrated")
        print(f"\n📋 Next Steps:")
        print(f"1. Set up GCP credentials (see gcp_credentials_setup.md)")
        print(f"2. Configure project settings in gcp_project_config.py")
        print(f"3. Run: python master_controller.py")
        print(f"4. Enter a company name for full analysis")
        print(f"=" * 60)
        
    except Exception as e:
        print(f"❌ Demo failed: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # Cleanup
        if sample_file and os.path.exists(sample_file):
            try:
                os.unlink(sample_file)
                print(f"\n🧹 Cleaned up sample file")
            except Exception as e:
                print(f"⚠️ Failed to clean up: {e}")

if __name__ == "__main__":
    demo_pipeline_flow()
