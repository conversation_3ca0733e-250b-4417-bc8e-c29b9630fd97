# Core web scraping and HTTP requests
requests>=2.31.0
beautifulsoup4>=4.12.0

# Google APIs and Authentication
google-api-python-client>=2.100.0
google-generativeai>=0.8.0
google-auth>=2.17.0
google-auth-oauthlib>=1.0.0
google-auth-httplib2>=0.1.0

# Google Cloud Platform - Market Intelligence Pipeline
google-cloud-storage>=2.10.0
google-cloud-aiplatform>=1.38.0
google-cloud-core>=2.3.0

# YouTube transcript extraction
youtube-transcript-api>=0.6.0

# Reddit API
praw>=7.7.0

# LinkedIn scraping and Google Sheets integration
gspread>=5.10.0
oauth2client>=4.1.3

# Data processing and analysis
pandas>=2.0.0
numpy>=1.24.0

# Sentiment analysis
vaderSentiment>=3.3.2

# LangChain for AI/LLM integration
langchain-community>=0.0.20
langchain-google-genai>=1.0.0
langchain-core>=0.1.0

# Date and time handling
python-dateutil>=2.8.0

# HTML parsing (additional parsers for BeautifulSoup)
lxml>=4.9.0
html5lib>=1.1

# CSV handling and data loading
csv-loader>=0.1.0

# Additional utilities
typing-extensions>=4.5.0

# Optional: For better performance and additional features
# Uncomment if needed:
# selenium>=4.15.0  # For advanced web scraping
# openpyxl>=3.1.0   # For Excel file handling
# xlrd>=2.0.1       # For reading Excel files
# matplotlib>=3.6.0 # For data visualization
# seaborn>=0.12.0   # For statistical data visualization