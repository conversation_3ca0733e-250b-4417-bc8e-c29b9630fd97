#!/usr/bin/env python3
"""
Simple Integration Test for Market Intelligence Pipeline

This script performs a quick integration test to verify the pipeline
can process a sample report end-to-end.
"""

import os
import sys
import tempfile
from datetime import datetime

def create_test_report(company_name: str = "SampleCorp") -> str:
    """Create a simple test HTML report"""
    html_content = f"""
    <!DOCTYPE html>
    <html>
    <head>
        <title>Test Report - {company_name}</title>
    </head>
    <body>
        <h1>Market Analysis for {company_name}</h1>
        <p>This is a test market intelligence report generated on {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}.</p>
        
        <h2>Key Findings</h2>
        <ul>
            <li>{company_name} shows strong market position</li>
            <li>Revenue growth of 10% year-over-year</li>
            <li>Expanding into new market segments</li>
            <li>Strong competitive advantages in technology</li>
        </ul>
        
        <h2>Market Trends</h2>
        <p>The market for {company_name}'s products is experiencing significant growth 
        driven by digital transformation and increased demand for innovative solutions.</p>
        
        <h2>Recommendations</h2>
        <ol>
            <li>Continue investment in R&D</li>
            <li>Expand market presence</li>
            <li>Focus on customer satisfaction</li>
        </ol>
    </body>
    </html>
    """
    
    # Create temporary file
    with tempfile.NamedTemporaryFile(mode='w', suffix='.html', delete=False, encoding='utf-8') as f:
        f.write(html_content)
        return f.name

def test_pipeline_integration():
    """Test the complete pipeline integration"""
    print("🧪 Market Intelligence Pipeline - Simple Integration Test")
    print("=" * 60)
    
    test_company = "SampleCorp"
    test_file = None
    
    try:
        # Step 1: Create test report
        print("\n📝 Step 1: Creating test HTML report...")
        test_file = create_test_report(test_company)
        print(f"✅ Test report created: {test_file}")
        
        # Step 2: Test pipeline import
        print("\n📦 Step 2: Testing pipeline import...")
        try:
            from market_intelligence_pipeline import MarketIntelligencePipeline
            print("✅ Pipeline imported successfully")
        except ImportError as e:
            print(f"❌ Failed to import pipeline: {e}")
            return False
        
        # Step 3: Initialize pipeline
        print("\n🔧 Step 3: Initializing pipeline...")
        try:
            pipeline = MarketIntelligencePipeline(enable_deduplication=True)
            print("✅ Pipeline initialized")
        except Exception as e:
            print(f"❌ Failed to initialize pipeline: {e}")
            print("   This is expected if GCP credentials are not set up")
            return False
        
        # Step 4: Health check
        print("\n🏥 Step 4: Performing health check...")
        health = pipeline.health_check()
        print(f"Overall status: {health['overall_status']}")
        
        healthy_components = 0
        total_components = len(health['components'])
        
        for component, status in health['components'].items():
            if status['status'] == 'healthy':
                print(f"✅ {component}: {status['message']}")
                healthy_components += 1
            elif status['status'] == 'disabled':
                print(f"⏭️ {component}: {status['message']}")
            else:
                print(f"❌ {component}: {status['message']}")
        
        print(f"Components healthy: {healthy_components}/{total_components}")
        
        # Step 5: Process test report
        print("\n🚀 Step 5: Processing test report...")
        try:
            result = pipeline.process_report(test_file, test_company)
            
            if result['success']:
                print("✅ Pipeline processing completed successfully!")
                print(f"   Duration: {result.get('duration_seconds', 0):.2f} seconds")
                print(f"   GCS URL: {result.get('final_gcs_url', 'N/A')}")
                print(f"   Chunks processed: {result.get('chunks_processed', 0)}")
                print(f"   Vectors stored: {result.get('vectors_stored', 0)}")
                
                # Show step results
                print("\n📋 Pipeline steps:")
                for step_name, step_result in result.get('steps', {}).items():
                    status = "✅" if step_result.get('success', False) else "❌"
                    print(f"   {status} {step_name}")
                
                return True
            else:
                print(f"❌ Pipeline processing failed: {result.get('error')}")
                print("\n📋 Pipeline steps:")
                for step_name, step_result in result.get('steps', {}).items():
                    status = "✅" if step_result.get('success', False) else "❌"
                    print(f"   {status} {step_name}")
                return False
                
        except Exception as e:
            print(f"❌ Pipeline processing failed: {e}")
            return False
        
    except Exception as e:
        print(f"❌ Integration test failed: {e}")
        return False
    
    finally:
        # Cleanup
        if test_file and os.path.exists(test_file):
            try:
                os.unlink(test_file)
                print(f"\n🧹 Cleaned up test file")
            except Exception as e:
                print(f"⚠️ Failed to clean up test file: {e}")

def main():
    """Main function"""
    print("Starting simple integration test...")
    
    success = test_pipeline_integration()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 Integration test PASSED!")
        print("\nThe pipeline is working correctly. You can now:")
        print("1. Run: python master_controller.py")
        print("2. Enter a company name")
        print("3. Wait for the complete analysis")
    else:
        print("❌ Integration test FAILED!")
        print("\nTo fix issues:")
        print("1. Run: python setup_environment.py")
        print("2. Follow the GCP setup guide")
        print("3. Ensure all credentials are properly configured")
    print("=" * 60)
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
