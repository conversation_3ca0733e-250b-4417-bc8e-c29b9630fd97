#!/usr/bin/env python3
"""
Environment Setup Script for Market Intelligence Pipeline

This script helps set up the environment and validates the installation.
"""

import os
import sys
import subprocess
import json
from pathlib import Path

def print_header(title):
    """Print a formatted header"""
    print(f"\n{'='*60}")
    print(f"🔧 {title}")
    print(f"{'='*60}")

def print_step(step_num, title):
    """Print a formatted step"""
    print(f"\n📋 Step {step_num}: {title}")
    print("-" * 40)

def check_python_version():
    """Check if Python version is compatible"""
    print_step(1, "Checking Python Version")
    
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print(f"❌ Python {version.major}.{version.minor} is not supported")
        print("   Minimum required: Python 3.8")
        return False
    
    print(f"✅ Python {version.major}.{version.minor}.{version.micro} is compatible")
    return True

def install_requirements():
    """Install Python requirements"""
    print_step(2, "Installing Python Requirements")
    
    if not os.path.exists("requirements.txt"):
        print("❌ requirements.txt not found")
        return False
    
    try:
        print("📦 Installing packages...")
        result = subprocess.run([
            sys.executable, "-m", "pip", "install", "-r", "requirements.txt"
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ All packages installed successfully")
            return True
        else:
            print(f"❌ Package installation failed:")
            print(result.stderr)
            return False
    except Exception as e:
        print(f"❌ Error installing packages: {e}")
        return False

def check_gcp_configuration():
    """Check GCP configuration files"""
    print_step(3, "Checking GCP Configuration")
    
    # Check if configuration files exist
    config_files = {
        "gcp_project_config.py": "GCP project configuration",
        "gcp_storage_credentials.json": "GCS service account credentials",
        "gcp_vertex_ai_credentials.json": "Vertex AI service account credentials"
    }
    
    all_exist = True
    for file_path, description in config_files.items():
        if os.path.exists(file_path):
            print(f"✅ {description}: Found")
        else:
            print(f"❌ {description}: Missing ({file_path})")
            all_exist = False
    
    if not all_exist:
        print("\n📖 Please follow the GCP setup guide:")
        print("   1. Read gcp_credentials_setup.md")
        print("   2. Create the missing credential files")
        print("   3. Update gcp_project_config.py with your settings")
        return False
    
    return True

def validate_gcp_credentials():
    """Validate GCP credentials"""
    print_step(4, "Validating GCP Credentials")
    
    try:
        # Import and run the validator
        result = subprocess.run([
            sys.executable, "gcp_credentials_validator.py"
        ], capture_output=True, text=True)
        
        print(result.stdout)
        if result.stderr:
            print(result.stderr)
        
        if result.returncode == 0:
            print("✅ GCP credentials validation passed")
            return True
        else:
            print("❌ GCP credentials validation failed")
            return False
    except Exception as e:
        print(f"❌ Error validating GCP credentials: {e}")
        return False

def test_pipeline_components():
    """Test individual pipeline components"""
    print_step(5, "Testing Pipeline Components")
    
    components = [
        ("gcs_storage_manager.py", "GCS Storage Manager"),
        ("vertex_ai_embedding_service.py", "Vertex AI Embedding Service"),
        ("vertex_ai_matching_engine.py", "Vertex AI Matching Engine"),
        ("market_intelligence_pipeline.py", "Complete Pipeline")
    ]
    
    all_passed = True
    for script, name in components:
        try:
            print(f"🧪 Testing {name}...")
            result = subprocess.run([
                sys.executable, script
            ], capture_output=True, text=True, timeout=30)
            
            if result.returncode == 0:
                print(f"✅ {name}: Test passed")
            else:
                print(f"❌ {name}: Test failed")
                if result.stderr:
                    print(f"   Error: {result.stderr[:200]}...")
                all_passed = False
        except subprocess.TimeoutExpired:
            print(f"⏰ {name}: Test timed out (may still be working)")
        except Exception as e:
            print(f"❌ {name}: Test error - {e}")
            all_passed = False
    
    return all_passed

def create_sample_config():
    """Create sample configuration if needed"""
    print_step(6, "Creating Sample Configuration")
    
    if not os.path.exists("gcp_project_config.py"):
        print("📝 Creating sample gcp_project_config.py...")
        # The file already exists from our setup, so this is just a check
        print("✅ Configuration file already exists")
    else:
        print("✅ Configuration file already exists")
    
    return True

def main():
    """Main setup function"""
    print_header("Market Intelligence Pipeline - Environment Setup")
    
    print("This script will help you set up the Market Intelligence Pipeline.")
    print("It will check dependencies, validate configuration, and test components.")
    
    # Step-by-step setup
    steps = [
        ("Python Version", check_python_version),
        ("Install Requirements", install_requirements),
        ("GCP Configuration", check_gcp_configuration),
        ("Validate Credentials", validate_gcp_credentials),
        ("Test Components", test_pipeline_components),
        ("Sample Configuration", create_sample_config)
    ]
    
    results = {}
    for step_name, step_function in steps:
        try:
            results[step_name] = step_function()
        except KeyboardInterrupt:
            print(f"\n⚠️ Setup interrupted by user")
            return False
        except Exception as e:
            print(f"\n❌ Unexpected error in {step_name}: {e}")
            results[step_name] = False
    
    # Summary
    print_header("Setup Summary")
    
    passed = 0
    total = len(results)
    
    for step_name, success in results.items():
        status = "✅ PASSED" if success else "❌ FAILED"
        print(f"{status} {step_name}")
        if success:
            passed += 1
    
    print(f"\nOverall: {passed}/{total} steps completed successfully")
    
    if passed == total:
        print("\n🎉 Environment setup completed successfully!")
        print("\nNext steps:")
        print("1. Run: python master_controller.py")
        print("2. Enter a company name when prompted")
        print("3. Wait for the complete analysis to finish")
        print("4. Check the generated HTML report and GCS storage")
        return True
    else:
        print("\n⚠️ Some setup steps failed. Please address the issues above.")
        print("\nFor help:")
        print("1. Check SETUP_GUIDE.md for detailed instructions")
        print("2. Review gcp_credentials_setup.md for GCP setup")
        print("3. Run individual test scripts to debug issues")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
